// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.6
// Source: docvault.proto

package documentlibrary

import (
	"context"

	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	ApprovalInfo                    = docvault.ApprovalInfo
	ApprovalInfoItem                = docvault.ApprovalInfoItem
	BookInfo                        = docvault.BookInfo
	BorrowDocumentItem              = docvault.BorrowDocumentItem
	BorrowRecordCreateReq           = docvault.BorrowRecordCreateReq
	BorrowRecordCreateResp          = docvault.BorrowRecordCreateResp
	CreateBookReq                   = docvault.CreateBookReq
	CreateBookResp                  = docvault.CreateBookResp
	DeleteBookReq                   = docvault.DeleteBookReq
	DeleteBookResp                  = docvault.DeleteBookResp
	DeleteDistributeReq             = docvault.DeleteDistributeReq
	DisposalApprovalInfo            = docvault.DisposalApprovalInfo
	DisposalList                    = docvault.DisposalList
	DistributeApprovalReq           = docvault.DistributeApprovalReq
	DistributeDetailInfo            = docvault.DistributeDetailInfo
	DistributeInventory             = docvault.DistributeInventory
	DistributeInventoryDetail       = docvault.DistributeInventoryDetail
	DistributeList                  = docvault.DistributeList
	DistributeUser                  = docvault.DistributeUser
	DistributeUserDetail            = docvault.DistributeUserDetail
	DocumentDistributeReq           = docvault.DocumentDistributeReq
	EmptyResp                       = docvault.EmptyResp
	ExternalDocumentChangeReq       = docvault.ExternalDocumentChangeReq
	ExternalDocumentCreateInfo      = docvault.ExternalDocumentCreateInfo
	ExternalDocumentCreateReq       = docvault.ExternalDocumentCreateReq
	ExternalDocumentCreateResp      = docvault.ExternalDocumentCreateResp
	ExternalDocumentCreateRespList  = docvault.ExternalDocumentCreateRespList
	ExternalDocumentGetReq          = docvault.ExternalDocumentGetReq
	ExternalDocumentGetResp         = docvault.ExternalDocumentGetResp
	ExternalDocumentPageInfo        = docvault.ExternalDocumentPageInfo
	ExternalDocumentPageReq         = docvault.ExternalDocumentPageReq
	ExternalDocumentPageResp        = docvault.ExternalDocumentPageResp
	FilePermission                  = docvault.FilePermission
	GetBookListReq                  = docvault.GetBookListReq
	GetBookListResp                 = docvault.GetBookListResp
	GetDistributeApplicationReq     = docvault.GetDistributeApplicationReq
	GetDistributeApplicationResp    = docvault.GetDistributeApplicationResp
	GetDistributeDetailReq          = docvault.GetDistributeDetailReq
	GetDistributeDetailResp         = docvault.GetDistributeDetailResp
	GetDistributeListInfo           = docvault.GetDistributeListInfo
	GetDistributeListReq            = docvault.GetDistributeListReq
	GetDistributeListResp           = docvault.GetDistributeListResp
	GetDocPermissionUsersReq        = docvault.GetDocPermissionUsersReq
	GetDocPermissionUsersResp       = docvault.GetDocPermissionUsersResp
	GetRecycleInfoReq               = docvault.GetRecycleInfoReq
	GetRecycleInfoResp              = docvault.GetRecycleInfoResp
	HandoverPerson                  = docvault.HandoverPerson
	ImportBookInfo                  = docvault.ImportBookInfo
	ImportBookReq                   = docvault.ImportBookReq
	ImportBookResp                  = docvault.ImportBookResp
	ImportGroupDocsToCompanyInfo    = docvault.ImportGroupDocsToCompanyInfo
	ImportGroupDocsToCompanyReq     = docvault.ImportGroupDocsToCompanyReq
	ImportGroupDocsToCompanyResp    = docvault.ImportGroupDocsToCompanyResp
	ImportRespInfo                  = docvault.ImportRespInfo
	InternalDocumentBatchCreateReq  = docvault.InternalDocumentBatchCreateReq
	InternalDocumentBatchCreateResp = docvault.InternalDocumentBatchCreateResp
	InternalDocumentChangeReq       = docvault.InternalDocumentChangeReq
	InternalDocumentCreateReq       = docvault.InternalDocumentCreateReq
	InternalDocumentCreateResp      = docvault.InternalDocumentCreateResp
	InternalDocumentGetReq          = docvault.InternalDocumentGetReq
	InternalDocumentGetResp         = docvault.InternalDocumentGetResp
	InternalDocumentPageItem        = docvault.InternalDocumentPageItem
	InternalDocumentPageReq         = docvault.InternalDocumentPageReq
	InternalDocumentPageResp        = docvault.InternalDocumentPageResp
	PageInfo                        = docvault.PageInfo
	Permission                      = docvault.Permission
	PermissionDetail                = docvault.PermissionDetail
	PermissionResp                  = docvault.PermissionResp
	PlagiarismCheckReq              = docvault.PlagiarismCheckReq
	ReceivedBy                      = docvault.ReceivedBy
	Recipient                       = docvault.Recipient
	RecycleApprovalInfo             = docvault.RecycleApprovalInfo
	RecycleInfo                     = docvault.RecycleInfo
	RecycleList                     = docvault.RecycleList
	RecycleRecord                   = docvault.RecycleRecord
	UpdateBookResp                  = docvault.UpdateBookResp
	UpdateDistributeStatusReq       = docvault.UpdateDistributeStatusReq
	UpdateUserDisposalStatusReq     = docvault.UpdateUserDisposalStatusReq

	DocumentLibrary interface {
		// 保存发放记录
		PreSaveDistributeRecord(ctx context.Context, in *DocumentDistributeReq, opts ...grpc.CallOption) (*EmptyResp, error)
		// 获取文档权限用户
		GetDocPermissionUsers(ctx context.Context, in *GetDocPermissionUsersReq, opts ...grpc.CallOption) (*GetDocPermissionUsersResp, error)
		// 更新发放状态
		UpdateDistributeStatusByWorkflowId(ctx context.Context, in *UpdateDistributeStatusReq, opts ...grpc.CallOption) (*EmptyResp, error)
		// 保存发放审批信息
		SaveDistributeApproval(ctx context.Context, in *DistributeApprovalReq, opts ...grpc.CallOption) (*EmptyResp, error)
		// 保存回收审批信息
		SaveRecycleApprovalInfo(ctx context.Context, in *RecycleApprovalInfo, opts ...grpc.CallOption) (*EmptyResp, error)
		// 保存处置审批信息
		SaveDisposalApprovalInfo(ctx context.Context, in *DisposalApprovalInfo, opts ...grpc.CallOption) (*EmptyResp, error)
		// 获取发放信息列表
		GetDistributeInfoList(ctx context.Context, in *GetDistributeListReq, opts ...grpc.CallOption) (*GetDistributeListResp, error)
		// 根据发放列表id获取发放清单信息
		GetDistributeApplicationById(ctx context.Context, in *GetDistributeApplicationReq, opts ...grpc.CallOption) (*GetDistributeApplicationResp, error)
		// 删除发放记录
		DeleteDistributeRecord(ctx context.Context, in *DeleteDistributeReq, opts ...grpc.CallOption) (*EmptyResp, error)
		// 获取发放回收详情
		GetDistributeDetail(ctx context.Context, in *GetDistributeDetailReq, opts ...grpc.CallOption) (*GetDistributeDetailResp, error)
		// 根据发放清单ID查询回收信息
		GetRecycleInfoByDistributeRecordFileId(ctx context.Context, in *GetRecycleInfoReq, opts ...grpc.CallOption) (*GetRecycleInfoResp, error)
		GetRecycleInfoByDistributeId(ctx context.Context, in *GetRecycleInfoReq, opts ...grpc.CallOption) (*GetRecycleInfoResp, error)
		// 创建借阅记录
		CreateBorrowRecord(ctx context.Context, in *BorrowRecordCreateReq, opts ...grpc.CallOption) (*BorrowRecordCreateResp, error)
		// 更新用户处置状态
		UpdateUserDisposalStatus(ctx context.Context, in *UpdateUserDisposalStatusReq, opts ...grpc.CallOption) (*EmptyResp, error)
	}

	defaultDocumentLibrary struct {
		cli zrpc.Client
	}
)

func NewDocumentLibrary(cli zrpc.Client) DocumentLibrary {
	return &defaultDocumentLibrary{
		cli: cli,
	}
}

// 保存发放记录
func (m *defaultDocumentLibrary) PreSaveDistributeRecord(ctx context.Context, in *DocumentDistributeReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.PreSaveDistributeRecord(ctx, in, opts...)
}

// 获取文档权限用户
func (m *defaultDocumentLibrary) GetDocPermissionUsers(ctx context.Context, in *GetDocPermissionUsersReq, opts ...grpc.CallOption) (*GetDocPermissionUsersResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.GetDocPermissionUsers(ctx, in, opts...)
}

// 更新发放状态
func (m *defaultDocumentLibrary) UpdateDistributeStatusByWorkflowId(ctx context.Context, in *UpdateDistributeStatusReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.UpdateDistributeStatusByWorkflowId(ctx, in, opts...)
}

// 保存发放审批信息
func (m *defaultDocumentLibrary) SaveDistributeApproval(ctx context.Context, in *DistributeApprovalReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.SaveDistributeApproval(ctx, in, opts...)
}

// 保存回收审批信息
func (m *defaultDocumentLibrary) SaveRecycleApprovalInfo(ctx context.Context, in *RecycleApprovalInfo, opts ...grpc.CallOption) (*EmptyResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.SaveRecycleApprovalInfo(ctx, in, opts...)
}

// 保存处置审批信息
func (m *defaultDocumentLibrary) SaveDisposalApprovalInfo(ctx context.Context, in *DisposalApprovalInfo, opts ...grpc.CallOption) (*EmptyResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.SaveDisposalApprovalInfo(ctx, in, opts...)
}

// 获取发放信息列表
func (m *defaultDocumentLibrary) GetDistributeInfoList(ctx context.Context, in *GetDistributeListReq, opts ...grpc.CallOption) (*GetDistributeListResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.GetDistributeInfoList(ctx, in, opts...)
}

// 根据发放列表id获取发放清单信息
func (m *defaultDocumentLibrary) GetDistributeApplicationById(ctx context.Context, in *GetDistributeApplicationReq, opts ...grpc.CallOption) (*GetDistributeApplicationResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.GetDistributeApplicationById(ctx, in, opts...)
}

// 删除发放记录
func (m *defaultDocumentLibrary) DeleteDistributeRecord(ctx context.Context, in *DeleteDistributeReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.DeleteDistributeRecord(ctx, in, opts...)
}

// 获取发放回收详情
func (m *defaultDocumentLibrary) GetDistributeDetail(ctx context.Context, in *GetDistributeDetailReq, opts ...grpc.CallOption) (*GetDistributeDetailResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.GetDistributeDetail(ctx, in, opts...)
}

// 根据发放清单ID查询回收信息
func (m *defaultDocumentLibrary) GetRecycleInfoByDistributeRecordFileId(ctx context.Context, in *GetRecycleInfoReq, opts ...grpc.CallOption) (*GetRecycleInfoResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.GetRecycleInfoByDistributeRecordFileId(ctx, in, opts...)
}

func (m *defaultDocumentLibrary) GetRecycleInfoByDistributeId(ctx context.Context, in *GetRecycleInfoReq, opts ...grpc.CallOption) (*GetRecycleInfoResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.GetRecycleInfoByDistributeId(ctx, in, opts...)
}

// 创建借阅记录
func (m *defaultDocumentLibrary) CreateBorrowRecord(ctx context.Context, in *BorrowRecordCreateReq, opts ...grpc.CallOption) (*BorrowRecordCreateResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.CreateBorrowRecord(ctx, in, opts...)
}

// 更新用户处置状态
func (m *defaultDocumentLibrary) UpdateUserDisposalStatus(ctx context.Context, in *UpdateUserDisposalStatusReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	client := docvault.NewDocumentLibraryClient(m.cli.Conn())
	return client.UpdateUserDisposalStatus(ctx, in, opts...)
}
