// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.6
// Source: docvault.proto

package internaldocumentlibrary

import (
	"context"

	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	ApprovalInfo                    = docvault.ApprovalInfo
	ApprovalInfoItem                = docvault.ApprovalInfoItem
	BookInfo                        = docvault.BookInfo
	BorrowDocumentItem              = docvault.BorrowDocumentItem
	BorrowRecordCreateReq           = docvault.BorrowRecordCreateReq
	BorrowRecordCreateResp          = docvault.BorrowRecordCreateResp
	CreateBookReq                   = docvault.CreateBookReq
	CreateBookResp                  = docvault.CreateBookResp
	DeleteBookReq                   = docvault.DeleteBookReq
	DeleteBookResp                  = docvault.DeleteBookResp
	DeleteDistributeReq             = docvault.DeleteDistributeReq
	DisposalApprovalInfo            = docvault.DisposalApprovalInfo
	DisposalList                    = docvault.DisposalList
	DistributeApprovalReq           = docvault.DistributeApprovalReq
	DistributeDetailInfo            = docvault.DistributeDetailInfo
	DistributeInventory             = docvault.DistributeInventory
	DistributeInventoryDetail       = docvault.DistributeInventoryDetail
	DistributeList                  = docvault.DistributeList
	DistributeUser                  = docvault.DistributeUser
	DistributeUserDetail            = docvault.DistributeUserDetail
	DocumentDistributeReq           = docvault.DocumentDistributeReq
	EmptyResp                       = docvault.EmptyResp
	ExternalDocumentChangeReq       = docvault.ExternalDocumentChangeReq
	ExternalDocumentCreateInfo      = docvault.ExternalDocumentCreateInfo
	ExternalDocumentCreateReq       = docvault.ExternalDocumentCreateReq
	ExternalDocumentCreateResp      = docvault.ExternalDocumentCreateResp
	ExternalDocumentCreateRespList  = docvault.ExternalDocumentCreateRespList
	ExternalDocumentGetReq          = docvault.ExternalDocumentGetReq
	ExternalDocumentGetResp         = docvault.ExternalDocumentGetResp
	ExternalDocumentPageInfo        = docvault.ExternalDocumentPageInfo
	ExternalDocumentPageReq         = docvault.ExternalDocumentPageReq
	ExternalDocumentPageResp        = docvault.ExternalDocumentPageResp
	FilePermission                  = docvault.FilePermission
	GetBookListReq                  = docvault.GetBookListReq
	GetBookListResp                 = docvault.GetBookListResp
	GetDistributeApplicationReq     = docvault.GetDistributeApplicationReq
	GetDistributeApplicationResp    = docvault.GetDistributeApplicationResp
	GetDistributeDetailReq          = docvault.GetDistributeDetailReq
	GetDistributeDetailResp         = docvault.GetDistributeDetailResp
	GetDistributeListInfo           = docvault.GetDistributeListInfo
	GetDistributeListReq            = docvault.GetDistributeListReq
	GetDistributeListResp           = docvault.GetDistributeListResp
	GetDocPermissionUsersReq        = docvault.GetDocPermissionUsersReq
	GetDocPermissionUsersResp       = docvault.GetDocPermissionUsersResp
	GetRecycleInfoReq               = docvault.GetRecycleInfoReq
	GetRecycleInfoResp              = docvault.GetRecycleInfoResp
	HandoverPerson                  = docvault.HandoverPerson
	ImportBookInfo                  = docvault.ImportBookInfo
	ImportBookReq                   = docvault.ImportBookReq
	ImportBookResp                  = docvault.ImportBookResp
	ImportGroupDocsToCompanyInfo    = docvault.ImportGroupDocsToCompanyInfo
	ImportGroupDocsToCompanyReq     = docvault.ImportGroupDocsToCompanyReq
	ImportGroupDocsToCompanyResp    = docvault.ImportGroupDocsToCompanyResp
	ImportRespInfo                  = docvault.ImportRespInfo
	InternalDocumentBatchCreateReq  = docvault.InternalDocumentBatchCreateReq
	InternalDocumentBatchCreateResp = docvault.InternalDocumentBatchCreateResp
	InternalDocumentChangeReq       = docvault.InternalDocumentChangeReq
	InternalDocumentCreateReq       = docvault.InternalDocumentCreateReq
	InternalDocumentCreateResp      = docvault.InternalDocumentCreateResp
	InternalDocumentGetReq          = docvault.InternalDocumentGetReq
	InternalDocumentGetResp         = docvault.InternalDocumentGetResp
	InternalDocumentPageItem        = docvault.InternalDocumentPageItem
	InternalDocumentPageReq         = docvault.InternalDocumentPageReq
	InternalDocumentPageResp        = docvault.InternalDocumentPageResp
	PageInfo                        = docvault.PageInfo
	Permission                      = docvault.Permission
	PermissionDetail                = docvault.PermissionDetail
	PermissionResp                  = docvault.PermissionResp
	PlagiarismCheckReq              = docvault.PlagiarismCheckReq
	ReceivedBy                      = docvault.ReceivedBy
	Recipient                       = docvault.Recipient
	RecycleApprovalInfo             = docvault.RecycleApprovalInfo
	RecycleInfo                     = docvault.RecycleInfo
	RecycleList                     = docvault.RecycleList
	RecycleRecord                   = docvault.RecycleRecord
	UpdateBookResp                  = docvault.UpdateBookResp
	UpdateDistributeStatusReq       = docvault.UpdateDistributeStatusReq
	UpdateUserDisposalStatusReq     = docvault.UpdateUserDisposalStatusReq

	InternalDocumentLibrary interface {
		// 创建文档
		Create(ctx context.Context, in *InternalDocumentCreateReq, opts ...grpc.CallOption) (*InternalDocumentCreateResp, error)
		// 获取文档
		Get(ctx context.Context, in *InternalDocumentGetReq, opts ...grpc.CallOption) (*InternalDocumentGetResp, error)
		// 分页查询文档
		Page(ctx context.Context, in *InternalDocumentPageReq, opts ...grpc.CallOption) (*InternalDocumentPageResp, error)
		// 修订文档
		Change(ctx context.Context, in *InternalDocumentChangeReq, opts ...grpc.CallOption) (*EmptyResp, error)
		// 批量创建文档
		BatchCreate(ctx context.Context, in *InternalDocumentBatchCreateReq, opts ...grpc.CallOption) (*InternalDocumentBatchCreateResp, error)
	}

	defaultInternalDocumentLibrary struct {
		cli zrpc.Client
	}
)

func NewInternalDocumentLibrary(cli zrpc.Client) InternalDocumentLibrary {
	return &defaultInternalDocumentLibrary{
		cli: cli,
	}
}

// 创建文档
func (m *defaultInternalDocumentLibrary) Create(ctx context.Context, in *InternalDocumentCreateReq, opts ...grpc.CallOption) (*InternalDocumentCreateResp, error) {
	client := docvault.NewInternalDocumentLibraryClient(m.cli.Conn())
	return client.Create(ctx, in, opts...)
}

// 获取文档
func (m *defaultInternalDocumentLibrary) Get(ctx context.Context, in *InternalDocumentGetReq, opts ...grpc.CallOption) (*InternalDocumentGetResp, error) {
	client := docvault.NewInternalDocumentLibraryClient(m.cli.Conn())
	return client.Get(ctx, in, opts...)
}

// 分页查询文档
func (m *defaultInternalDocumentLibrary) Page(ctx context.Context, in *InternalDocumentPageReq, opts ...grpc.CallOption) (*InternalDocumentPageResp, error) {
	client := docvault.NewInternalDocumentLibraryClient(m.cli.Conn())
	return client.Page(ctx, in, opts...)
}

// 修订文档
func (m *defaultInternalDocumentLibrary) Change(ctx context.Context, in *InternalDocumentChangeReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	client := docvault.NewInternalDocumentLibraryClient(m.cli.Conn())
	return client.Change(ctx, in, opts...)
}

// 批量创建文档
func (m *defaultInternalDocumentLibrary) BatchCreate(ctx context.Context, in *InternalDocumentBatchCreateReq, opts ...grpc.CallOption) (*InternalDocumentBatchCreateResp, error) {
	client := docvault.NewInternalDocumentLibraryClient(m.cli.Conn())
	return client.BatchCreate(ctx, in, opts...)
}
