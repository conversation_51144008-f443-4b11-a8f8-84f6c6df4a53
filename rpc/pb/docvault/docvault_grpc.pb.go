// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.6
// source: rpc/docvault.proto

package docvault

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	InternalDocumentLibrary_Create_FullMethodName      = "/docvault.InternalDocumentLibrary/Create"
	InternalDocumentLibrary_Get_FullMethodName         = "/docvault.InternalDocumentLibrary/Get"
	InternalDocumentLibrary_Page_FullMethodName        = "/docvault.InternalDocumentLibrary/Page"
	InternalDocumentLibrary_Change_FullMethodName      = "/docvault.InternalDocumentLibrary/Change"
	InternalDocumentLibrary_BatchCreate_FullMethodName = "/docvault.InternalDocumentLibrary/BatchCreate"
)

// InternalDocumentLibraryClient is the client API for InternalDocumentLibrary service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 内部文档库
type InternalDocumentLibraryClient interface {
	// 创建文档
	Create(ctx context.Context, in *InternalDocumentCreateReq, opts ...grpc.CallOption) (*InternalDocumentCreateResp, error)
	// 获取文档
	Get(ctx context.Context, in *InternalDocumentGetReq, opts ...grpc.CallOption) (*InternalDocumentGetResp, error)
	// 分页查询文档
	Page(ctx context.Context, in *InternalDocumentPageReq, opts ...grpc.CallOption) (*InternalDocumentPageResp, error)
	// 修订文档
	Change(ctx context.Context, in *InternalDocumentChangeReq, opts ...grpc.CallOption) (*EmptyResp, error)
	// 批量创建文档
	BatchCreate(ctx context.Context, in *InternalDocumentBatchCreateReq, opts ...grpc.CallOption) (*InternalDocumentBatchCreateResp, error)
}

type internalDocumentLibraryClient struct {
	cc grpc.ClientConnInterface
}

func NewInternalDocumentLibraryClient(cc grpc.ClientConnInterface) InternalDocumentLibraryClient {
	return &internalDocumentLibraryClient{cc}
}

func (c *internalDocumentLibraryClient) Create(ctx context.Context, in *InternalDocumentCreateReq, opts ...grpc.CallOption) (*InternalDocumentCreateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InternalDocumentCreateResp)
	err := c.cc.Invoke(ctx, InternalDocumentLibrary_Create_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *internalDocumentLibraryClient) Get(ctx context.Context, in *InternalDocumentGetReq, opts ...grpc.CallOption) (*InternalDocumentGetResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InternalDocumentGetResp)
	err := c.cc.Invoke(ctx, InternalDocumentLibrary_Get_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *internalDocumentLibraryClient) Page(ctx context.Context, in *InternalDocumentPageReq, opts ...grpc.CallOption) (*InternalDocumentPageResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InternalDocumentPageResp)
	err := c.cc.Invoke(ctx, InternalDocumentLibrary_Page_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *internalDocumentLibraryClient) Change(ctx context.Context, in *InternalDocumentChangeReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptyResp)
	err := c.cc.Invoke(ctx, InternalDocumentLibrary_Change_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *internalDocumentLibraryClient) BatchCreate(ctx context.Context, in *InternalDocumentBatchCreateReq, opts ...grpc.CallOption) (*InternalDocumentBatchCreateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InternalDocumentBatchCreateResp)
	err := c.cc.Invoke(ctx, InternalDocumentLibrary_BatchCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InternalDocumentLibraryServer is the server API for InternalDocumentLibrary service.
// All implementations must embed UnimplementedInternalDocumentLibraryServer
// for forward compatibility.
//
// 内部文档库
type InternalDocumentLibraryServer interface {
	// 创建文档
	Create(context.Context, *InternalDocumentCreateReq) (*InternalDocumentCreateResp, error)
	// 获取文档
	Get(context.Context, *InternalDocumentGetReq) (*InternalDocumentGetResp, error)
	// 分页查询文档
	Page(context.Context, *InternalDocumentPageReq) (*InternalDocumentPageResp, error)
	// 修订文档
	Change(context.Context, *InternalDocumentChangeReq) (*EmptyResp, error)
	// 批量创建文档
	BatchCreate(context.Context, *InternalDocumentBatchCreateReq) (*InternalDocumentBatchCreateResp, error)
	mustEmbedUnimplementedInternalDocumentLibraryServer()
}

// UnimplementedInternalDocumentLibraryServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedInternalDocumentLibraryServer struct{}

func (UnimplementedInternalDocumentLibraryServer) Create(context.Context, *InternalDocumentCreateReq) (*InternalDocumentCreateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedInternalDocumentLibraryServer) Get(context.Context, *InternalDocumentGetReq) (*InternalDocumentGetResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedInternalDocumentLibraryServer) Page(context.Context, *InternalDocumentPageReq) (*InternalDocumentPageResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Page not implemented")
}
func (UnimplementedInternalDocumentLibraryServer) Change(context.Context, *InternalDocumentChangeReq) (*EmptyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Change not implemented")
}
func (UnimplementedInternalDocumentLibraryServer) BatchCreate(context.Context, *InternalDocumentBatchCreateReq) (*InternalDocumentBatchCreateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCreate not implemented")
}
func (UnimplementedInternalDocumentLibraryServer) mustEmbedUnimplementedInternalDocumentLibraryServer() {
}
func (UnimplementedInternalDocumentLibraryServer) testEmbeddedByValue() {}

// UnsafeInternalDocumentLibraryServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to InternalDocumentLibraryServer will
// result in compilation errors.
type UnsafeInternalDocumentLibraryServer interface {
	mustEmbedUnimplementedInternalDocumentLibraryServer()
}

func RegisterInternalDocumentLibraryServer(s grpc.ServiceRegistrar, srv InternalDocumentLibraryServer) {
	// If the following call pancis, it indicates UnimplementedInternalDocumentLibraryServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&InternalDocumentLibrary_ServiceDesc, srv)
}

func _InternalDocumentLibrary_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InternalDocumentCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InternalDocumentLibraryServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InternalDocumentLibrary_Create_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InternalDocumentLibraryServer).Create(ctx, req.(*InternalDocumentCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InternalDocumentLibrary_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InternalDocumentGetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InternalDocumentLibraryServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InternalDocumentLibrary_Get_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InternalDocumentLibraryServer).Get(ctx, req.(*InternalDocumentGetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InternalDocumentLibrary_Page_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InternalDocumentPageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InternalDocumentLibraryServer).Page(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InternalDocumentLibrary_Page_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InternalDocumentLibraryServer).Page(ctx, req.(*InternalDocumentPageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InternalDocumentLibrary_Change_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InternalDocumentChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InternalDocumentLibraryServer).Change(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InternalDocumentLibrary_Change_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InternalDocumentLibraryServer).Change(ctx, req.(*InternalDocumentChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InternalDocumentLibrary_BatchCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InternalDocumentBatchCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InternalDocumentLibraryServer).BatchCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InternalDocumentLibrary_BatchCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InternalDocumentLibraryServer).BatchCreate(ctx, req.(*InternalDocumentBatchCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

// InternalDocumentLibrary_ServiceDesc is the grpc.ServiceDesc for InternalDocumentLibrary service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var InternalDocumentLibrary_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "docvault.InternalDocumentLibrary",
	HandlerType: (*InternalDocumentLibraryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Create",
			Handler:    _InternalDocumentLibrary_Create_Handler,
		},
		{
			MethodName: "Get",
			Handler:    _InternalDocumentLibrary_Get_Handler,
		},
		{
			MethodName: "Page",
			Handler:    _InternalDocumentLibrary_Page_Handler,
		},
		{
			MethodName: "Change",
			Handler:    _InternalDocumentLibrary_Change_Handler,
		},
		{
			MethodName: "BatchCreate",
			Handler:    _InternalDocumentLibrary_BatchCreate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rpc/docvault.proto",
}

const (
	Book_CreateBook_FullMethodName  = "/docvault.Book/CreateBook"
	Book_GetBookList_FullMethodName = "/docvault.Book/GetBookList"
	Book_UpdateBook_FullMethodName  = "/docvault.Book/UpdateBook"
	Book_DeleteBook_FullMethodName  = "/docvault.Book/DeleteBook"
	Book_ImportBook_FullMethodName  = "/docvault.Book/ImportBook"
)

// BookClient is the client API for Book service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 书籍操作
type BookClient interface {
	// 新增书籍信息
	CreateBook(ctx context.Context, in *CreateBookReq, opts ...grpc.CallOption) (*CreateBookResp, error)
	// 查询书籍列表
	GetBookList(ctx context.Context, in *GetBookListReq, opts ...grpc.CallOption) (*GetBookListResp, error)
	// 修改书籍信息
	UpdateBook(ctx context.Context, in *BookInfo, opts ...grpc.CallOption) (*UpdateBookResp, error)
	// 删除书籍信息
	DeleteBook(ctx context.Context, in *DeleteBookReq, opts ...grpc.CallOption) (*DeleteBookResp, error)
	// 批量导入书籍信息
	ImportBook(ctx context.Context, in *ImportBookReq, opts ...grpc.CallOption) (*ImportBookResp, error)
}

type bookClient struct {
	cc grpc.ClientConnInterface
}

func NewBookClient(cc grpc.ClientConnInterface) BookClient {
	return &bookClient{cc}
}

func (c *bookClient) CreateBook(ctx context.Context, in *CreateBookReq, opts ...grpc.CallOption) (*CreateBookResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBookResp)
	err := c.cc.Invoke(ctx, Book_CreateBook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookClient) GetBookList(ctx context.Context, in *GetBookListReq, opts ...grpc.CallOption) (*GetBookListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBookListResp)
	err := c.cc.Invoke(ctx, Book_GetBookList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookClient) UpdateBook(ctx context.Context, in *BookInfo, opts ...grpc.CallOption) (*UpdateBookResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBookResp)
	err := c.cc.Invoke(ctx, Book_UpdateBook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookClient) DeleteBook(ctx context.Context, in *DeleteBookReq, opts ...grpc.CallOption) (*DeleteBookResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteBookResp)
	err := c.cc.Invoke(ctx, Book_DeleteBook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookClient) ImportBook(ctx context.Context, in *ImportBookReq, opts ...grpc.CallOption) (*ImportBookResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ImportBookResp)
	err := c.cc.Invoke(ctx, Book_ImportBook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BookServer is the server API for Book service.
// All implementations must embed UnimplementedBookServer
// for forward compatibility.
//
// 书籍操作
type BookServer interface {
	// 新增书籍信息
	CreateBook(context.Context, *CreateBookReq) (*CreateBookResp, error)
	// 查询书籍列表
	GetBookList(context.Context, *GetBookListReq) (*GetBookListResp, error)
	// 修改书籍信息
	UpdateBook(context.Context, *BookInfo) (*UpdateBookResp, error)
	// 删除书籍信息
	DeleteBook(context.Context, *DeleteBookReq) (*DeleteBookResp, error)
	// 批量导入书籍信息
	ImportBook(context.Context, *ImportBookReq) (*ImportBookResp, error)
	mustEmbedUnimplementedBookServer()
}

// UnimplementedBookServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBookServer struct{}

func (UnimplementedBookServer) CreateBook(context.Context, *CreateBookReq) (*CreateBookResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBook not implemented")
}
func (UnimplementedBookServer) GetBookList(context.Context, *GetBookListReq) (*GetBookListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBookList not implemented")
}
func (UnimplementedBookServer) UpdateBook(context.Context, *BookInfo) (*UpdateBookResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBook not implemented")
}
func (UnimplementedBookServer) DeleteBook(context.Context, *DeleteBookReq) (*DeleteBookResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBook not implemented")
}
func (UnimplementedBookServer) ImportBook(context.Context, *ImportBookReq) (*ImportBookResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportBook not implemented")
}
func (UnimplementedBookServer) mustEmbedUnimplementedBookServer() {}
func (UnimplementedBookServer) testEmbeddedByValue()              {}

// UnsafeBookServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BookServer will
// result in compilation errors.
type UnsafeBookServer interface {
	mustEmbedUnimplementedBookServer()
}

func RegisterBookServer(s grpc.ServiceRegistrar, srv BookServer) {
	// If the following call pancis, it indicates UnimplementedBookServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Book_ServiceDesc, srv)
}

func _Book_CreateBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBookReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookServer).CreateBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Book_CreateBook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookServer).CreateBook(ctx, req.(*CreateBookReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Book_GetBookList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBookListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookServer).GetBookList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Book_GetBookList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookServer).GetBookList(ctx, req.(*GetBookListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Book_UpdateBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BookInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookServer).UpdateBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Book_UpdateBook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookServer).UpdateBook(ctx, req.(*BookInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Book_DeleteBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBookReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookServer).DeleteBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Book_DeleteBook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookServer).DeleteBook(ctx, req.(*DeleteBookReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Book_ImportBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportBookReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookServer).ImportBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Book_ImportBook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookServer).ImportBook(ctx, req.(*ImportBookReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Book_ServiceDesc is the grpc.ServiceDesc for Book service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Book_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "docvault.Book",
	HandlerType: (*BookServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBook",
			Handler:    _Book_CreateBook_Handler,
		},
		{
			MethodName: "GetBookList",
			Handler:    _Book_GetBookList_Handler,
		},
		{
			MethodName: "UpdateBook",
			Handler:    _Book_UpdateBook_Handler,
		},
		{
			MethodName: "DeleteBook",
			Handler:    _Book_DeleteBook_Handler,
		},
		{
			MethodName: "ImportBook",
			Handler:    _Book_ImportBook_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rpc/docvault.proto",
}

const (
	ExternalDocumentLibrary_Create_FullMethodName                       = "/docvault.ExternalDocumentLibrary/Create"
	ExternalDocumentLibrary_Get_FullMethodName                          = "/docvault.ExternalDocumentLibrary/Get"
	ExternalDocumentLibrary_Page_FullMethodName                         = "/docvault.ExternalDocumentLibrary/Page"
	ExternalDocumentLibrary_Change_FullMethodName                       = "/docvault.ExternalDocumentLibrary/Change"
	ExternalDocumentLibrary_ImportCompanyPlagiarismCheck_FullMethodName = "/docvault.ExternalDocumentLibrary/ImportCompanyPlagiarismCheck"
	ExternalDocumentLibrary_ImportGroupDocsToCompany_FullMethodName     = "/docvault.ExternalDocumentLibrary/ImportGroupDocsToCompany"
)

// ExternalDocumentLibraryClient is the client API for ExternalDocumentLibrary service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ExternalDocumentLibraryClient interface {
	// 批量创建文档
	Create(ctx context.Context, in *ExternalDocumentCreateReq, opts ...grpc.CallOption) (*ExternalDocumentCreateResp, error)
	// 获取文档
	Get(ctx context.Context, in *ExternalDocumentGetReq, opts ...grpc.CallOption) (*ExternalDocumentGetResp, error)
	// 分页查询文档
	Page(ctx context.Context, in *ExternalDocumentPageReq, opts ...grpc.CallOption) (*ExternalDocumentPageResp, error)
	// 修订文档
	Change(ctx context.Context, in *ExternalDocumentChangeReq, opts ...grpc.CallOption) (*EmptyResp, error)
	// 导入集团文档到公司验重
	ImportCompanyPlagiarismCheck(ctx context.Context, in *PlagiarismCheckReq, opts ...grpc.CallOption) (*EmptyResp, error)
	// 导入集团文档到公司
	ImportGroupDocsToCompany(ctx context.Context, in *ImportGroupDocsToCompanyReq, opts ...grpc.CallOption) (*ImportGroupDocsToCompanyResp, error)
}

type externalDocumentLibraryClient struct {
	cc grpc.ClientConnInterface
}

func NewExternalDocumentLibraryClient(cc grpc.ClientConnInterface) ExternalDocumentLibraryClient {
	return &externalDocumentLibraryClient{cc}
}

func (c *externalDocumentLibraryClient) Create(ctx context.Context, in *ExternalDocumentCreateReq, opts ...grpc.CallOption) (*ExternalDocumentCreateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExternalDocumentCreateResp)
	err := c.cc.Invoke(ctx, ExternalDocumentLibrary_Create_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *externalDocumentLibraryClient) Get(ctx context.Context, in *ExternalDocumentGetReq, opts ...grpc.CallOption) (*ExternalDocumentGetResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExternalDocumentGetResp)
	err := c.cc.Invoke(ctx, ExternalDocumentLibrary_Get_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *externalDocumentLibraryClient) Page(ctx context.Context, in *ExternalDocumentPageReq, opts ...grpc.CallOption) (*ExternalDocumentPageResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExternalDocumentPageResp)
	err := c.cc.Invoke(ctx, ExternalDocumentLibrary_Page_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *externalDocumentLibraryClient) Change(ctx context.Context, in *ExternalDocumentChangeReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptyResp)
	err := c.cc.Invoke(ctx, ExternalDocumentLibrary_Change_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *externalDocumentLibraryClient) ImportCompanyPlagiarismCheck(ctx context.Context, in *PlagiarismCheckReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptyResp)
	err := c.cc.Invoke(ctx, ExternalDocumentLibrary_ImportCompanyPlagiarismCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *externalDocumentLibraryClient) ImportGroupDocsToCompany(ctx context.Context, in *ImportGroupDocsToCompanyReq, opts ...grpc.CallOption) (*ImportGroupDocsToCompanyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ImportGroupDocsToCompanyResp)
	err := c.cc.Invoke(ctx, ExternalDocumentLibrary_ImportGroupDocsToCompany_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ExternalDocumentLibraryServer is the server API for ExternalDocumentLibrary service.
// All implementations must embed UnimplementedExternalDocumentLibraryServer
// for forward compatibility.
type ExternalDocumentLibraryServer interface {
	// 批量创建文档
	Create(context.Context, *ExternalDocumentCreateReq) (*ExternalDocumentCreateResp, error)
	// 获取文档
	Get(context.Context, *ExternalDocumentGetReq) (*ExternalDocumentGetResp, error)
	// 分页查询文档
	Page(context.Context, *ExternalDocumentPageReq) (*ExternalDocumentPageResp, error)
	// 修订文档
	Change(context.Context, *ExternalDocumentChangeReq) (*EmptyResp, error)
	// 导入集团文档到公司验重
	ImportCompanyPlagiarismCheck(context.Context, *PlagiarismCheckReq) (*EmptyResp, error)
	// 导入集团文档到公司
	ImportGroupDocsToCompany(context.Context, *ImportGroupDocsToCompanyReq) (*ImportGroupDocsToCompanyResp, error)
	mustEmbedUnimplementedExternalDocumentLibraryServer()
}

// UnimplementedExternalDocumentLibraryServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedExternalDocumentLibraryServer struct{}

func (UnimplementedExternalDocumentLibraryServer) Create(context.Context, *ExternalDocumentCreateReq) (*ExternalDocumentCreateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedExternalDocumentLibraryServer) Get(context.Context, *ExternalDocumentGetReq) (*ExternalDocumentGetResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedExternalDocumentLibraryServer) Page(context.Context, *ExternalDocumentPageReq) (*ExternalDocumentPageResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Page not implemented")
}
func (UnimplementedExternalDocumentLibraryServer) Change(context.Context, *ExternalDocumentChangeReq) (*EmptyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Change not implemented")
}
func (UnimplementedExternalDocumentLibraryServer) ImportCompanyPlagiarismCheck(context.Context, *PlagiarismCheckReq) (*EmptyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportCompanyPlagiarismCheck not implemented")
}
func (UnimplementedExternalDocumentLibraryServer) ImportGroupDocsToCompany(context.Context, *ImportGroupDocsToCompanyReq) (*ImportGroupDocsToCompanyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportGroupDocsToCompany not implemented")
}
func (UnimplementedExternalDocumentLibraryServer) mustEmbedUnimplementedExternalDocumentLibraryServer() {
}
func (UnimplementedExternalDocumentLibraryServer) testEmbeddedByValue() {}

// UnsafeExternalDocumentLibraryServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ExternalDocumentLibraryServer will
// result in compilation errors.
type UnsafeExternalDocumentLibraryServer interface {
	mustEmbedUnimplementedExternalDocumentLibraryServer()
}

func RegisterExternalDocumentLibraryServer(s grpc.ServiceRegistrar, srv ExternalDocumentLibraryServer) {
	// If the following call pancis, it indicates UnimplementedExternalDocumentLibraryServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ExternalDocumentLibrary_ServiceDesc, srv)
}

func _ExternalDocumentLibrary_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExternalDocumentCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExternalDocumentLibraryServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExternalDocumentLibrary_Create_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExternalDocumentLibraryServer).Create(ctx, req.(*ExternalDocumentCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExternalDocumentLibrary_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExternalDocumentGetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExternalDocumentLibraryServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExternalDocumentLibrary_Get_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExternalDocumentLibraryServer).Get(ctx, req.(*ExternalDocumentGetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExternalDocumentLibrary_Page_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExternalDocumentPageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExternalDocumentLibraryServer).Page(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExternalDocumentLibrary_Page_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExternalDocumentLibraryServer).Page(ctx, req.(*ExternalDocumentPageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExternalDocumentLibrary_Change_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExternalDocumentChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExternalDocumentLibraryServer).Change(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExternalDocumentLibrary_Change_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExternalDocumentLibraryServer).Change(ctx, req.(*ExternalDocumentChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExternalDocumentLibrary_ImportCompanyPlagiarismCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlagiarismCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExternalDocumentLibraryServer).ImportCompanyPlagiarismCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExternalDocumentLibrary_ImportCompanyPlagiarismCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExternalDocumentLibraryServer).ImportCompanyPlagiarismCheck(ctx, req.(*PlagiarismCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExternalDocumentLibrary_ImportGroupDocsToCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportGroupDocsToCompanyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExternalDocumentLibraryServer).ImportGroupDocsToCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExternalDocumentLibrary_ImportGroupDocsToCompany_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExternalDocumentLibraryServer).ImportGroupDocsToCompany(ctx, req.(*ImportGroupDocsToCompanyReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ExternalDocumentLibrary_ServiceDesc is the grpc.ServiceDesc for ExternalDocumentLibrary service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ExternalDocumentLibrary_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "docvault.ExternalDocumentLibrary",
	HandlerType: (*ExternalDocumentLibraryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Create",
			Handler:    _ExternalDocumentLibrary_Create_Handler,
		},
		{
			MethodName: "Get",
			Handler:    _ExternalDocumentLibrary_Get_Handler,
		},
		{
			MethodName: "Page",
			Handler:    _ExternalDocumentLibrary_Page_Handler,
		},
		{
			MethodName: "Change",
			Handler:    _ExternalDocumentLibrary_Change_Handler,
		},
		{
			MethodName: "ImportCompanyPlagiarismCheck",
			Handler:    _ExternalDocumentLibrary_ImportCompanyPlagiarismCheck_Handler,
		},
		{
			MethodName: "ImportGroupDocsToCompany",
			Handler:    _ExternalDocumentLibrary_ImportGroupDocsToCompany_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rpc/docvault.proto",
}

const (
	DocumentLibrary_PreSaveDistributeRecord_FullMethodName                = "/docvault.DocumentLibrary/PreSaveDistributeRecord"
	DocumentLibrary_GetDocPermissionUsers_FullMethodName                  = "/docvault.DocumentLibrary/GetDocPermissionUsers"
	DocumentLibrary_UpdateDistributeStatusByWorkflowId_FullMethodName     = "/docvault.DocumentLibrary/UpdateDistributeStatusByWorkflowId"
	DocumentLibrary_SaveDistributeApproval_FullMethodName                 = "/docvault.DocumentLibrary/SaveDistributeApproval"
	DocumentLibrary_SaveRecycleApprovalInfo_FullMethodName                = "/docvault.DocumentLibrary/SaveRecycleApprovalInfo"
	DocumentLibrary_SaveDisposalApprovalInfo_FullMethodName               = "/docvault.DocumentLibrary/SaveDisposalApprovalInfo"
	DocumentLibrary_GetDistributeInfoList_FullMethodName                  = "/docvault.DocumentLibrary/GetDistributeInfoList"
	DocumentLibrary_GetDistributeApplicationById_FullMethodName           = "/docvault.DocumentLibrary/GetDistributeApplicationById"
	DocumentLibrary_DeleteDistributeRecord_FullMethodName                 = "/docvault.DocumentLibrary/DeleteDistributeRecord"
	DocumentLibrary_GetDistributeDetail_FullMethodName                    = "/docvault.DocumentLibrary/GetDistributeDetail"
	DocumentLibrary_GetRecycleInfoByDistributeRecordFileId_FullMethodName = "/docvault.DocumentLibrary/GetRecycleInfoByDistributeRecordFileId"
	DocumentLibrary_GetRecycleInfoByDistributeId_FullMethodName           = "/docvault.DocumentLibrary/GetRecycleInfoByDistributeId"
	DocumentLibrary_CreateBorrowRecord_FullMethodName                     = "/docvault.DocumentLibrary/CreateBorrowRecord"
	DocumentLibrary_UpdateUserDisposalStatus_FullMethodName               = "/docvault.DocumentLibrary/UpdateUserDisposalStatus"
)

// DocumentLibraryClient is the client API for DocumentLibrary service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DocumentLibraryClient interface {
	// 保存发放记录
	PreSaveDistributeRecord(ctx context.Context, in *DocumentDistributeReq, opts ...grpc.CallOption) (*EmptyResp, error)
	// 获取文档权限用户
	GetDocPermissionUsers(ctx context.Context, in *GetDocPermissionUsersReq, opts ...grpc.CallOption) (*GetDocPermissionUsersResp, error)
	// 更新发放状态
	UpdateDistributeStatusByWorkflowId(ctx context.Context, in *UpdateDistributeStatusReq, opts ...grpc.CallOption) (*EmptyResp, error)
	// 保存发放审批信息
	SaveDistributeApproval(ctx context.Context, in *DistributeApprovalReq, opts ...grpc.CallOption) (*EmptyResp, error)
	// 保存回收审批信息
	SaveRecycleApprovalInfo(ctx context.Context, in *RecycleApprovalInfo, opts ...grpc.CallOption) (*EmptyResp, error)
	// 保存处置审批信息
	SaveDisposalApprovalInfo(ctx context.Context, in *DisposalApprovalInfo, opts ...grpc.CallOption) (*EmptyResp, error)
	// 获取发放信息列表
	GetDistributeInfoList(ctx context.Context, in *GetDistributeListReq, opts ...grpc.CallOption) (*GetDistributeListResp, error)
	// 根据发放列表id获取发放清单信息
	GetDistributeApplicationById(ctx context.Context, in *GetDistributeApplicationReq, opts ...grpc.CallOption) (*GetDistributeApplicationResp, error)
	// 删除发放记录
	DeleteDistributeRecord(ctx context.Context, in *DeleteDistributeReq, opts ...grpc.CallOption) (*EmptyResp, error)
	// 获取发放回收详情
	GetDistributeDetail(ctx context.Context, in *GetDistributeDetailReq, opts ...grpc.CallOption) (*GetDistributeDetailResp, error)
	// 根据发放清单ID查询回收信息
	GetRecycleInfoByDistributeRecordFileId(ctx context.Context, in *GetRecycleInfoReq, opts ...grpc.CallOption) (*GetRecycleInfoResp, error)
	GetRecycleInfoByDistributeId(ctx context.Context, in *GetRecycleInfoReq, opts ...grpc.CallOption) (*GetRecycleInfoResp, error)
	// 创建借阅记录
	CreateBorrowRecord(ctx context.Context, in *BorrowRecordCreateReq, opts ...grpc.CallOption) (*BorrowRecordCreateResp, error)
	// 更新用户处置状态
	UpdateUserDisposalStatus(ctx context.Context, in *UpdateUserDisposalStatusReq, opts ...grpc.CallOption) (*EmptyResp, error)
}

type documentLibraryClient struct {
	cc grpc.ClientConnInterface
}

func NewDocumentLibraryClient(cc grpc.ClientConnInterface) DocumentLibraryClient {
	return &documentLibraryClient{cc}
}

func (c *documentLibraryClient) PreSaveDistributeRecord(ctx context.Context, in *DocumentDistributeReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptyResp)
	err := c.cc.Invoke(ctx, DocumentLibrary_PreSaveDistributeRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *documentLibraryClient) GetDocPermissionUsers(ctx context.Context, in *GetDocPermissionUsersReq, opts ...grpc.CallOption) (*GetDocPermissionUsersResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDocPermissionUsersResp)
	err := c.cc.Invoke(ctx, DocumentLibrary_GetDocPermissionUsers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *documentLibraryClient) UpdateDistributeStatusByWorkflowId(ctx context.Context, in *UpdateDistributeStatusReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptyResp)
	err := c.cc.Invoke(ctx, DocumentLibrary_UpdateDistributeStatusByWorkflowId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *documentLibraryClient) SaveDistributeApproval(ctx context.Context, in *DistributeApprovalReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptyResp)
	err := c.cc.Invoke(ctx, DocumentLibrary_SaveDistributeApproval_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *documentLibraryClient) SaveRecycleApprovalInfo(ctx context.Context, in *RecycleApprovalInfo, opts ...grpc.CallOption) (*EmptyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptyResp)
	err := c.cc.Invoke(ctx, DocumentLibrary_SaveRecycleApprovalInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *documentLibraryClient) SaveDisposalApprovalInfo(ctx context.Context, in *DisposalApprovalInfo, opts ...grpc.CallOption) (*EmptyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptyResp)
	err := c.cc.Invoke(ctx, DocumentLibrary_SaveDisposalApprovalInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *documentLibraryClient) GetDistributeInfoList(ctx context.Context, in *GetDistributeListReq, opts ...grpc.CallOption) (*GetDistributeListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDistributeListResp)
	err := c.cc.Invoke(ctx, DocumentLibrary_GetDistributeInfoList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *documentLibraryClient) GetDistributeApplicationById(ctx context.Context, in *GetDistributeApplicationReq, opts ...grpc.CallOption) (*GetDistributeApplicationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDistributeApplicationResp)
	err := c.cc.Invoke(ctx, DocumentLibrary_GetDistributeApplicationById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *documentLibraryClient) DeleteDistributeRecord(ctx context.Context, in *DeleteDistributeReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptyResp)
	err := c.cc.Invoke(ctx, DocumentLibrary_DeleteDistributeRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *documentLibraryClient) GetDistributeDetail(ctx context.Context, in *GetDistributeDetailReq, opts ...grpc.CallOption) (*GetDistributeDetailResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDistributeDetailResp)
	err := c.cc.Invoke(ctx, DocumentLibrary_GetDistributeDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *documentLibraryClient) GetRecycleInfoByDistributeRecordFileId(ctx context.Context, in *GetRecycleInfoReq, opts ...grpc.CallOption) (*GetRecycleInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetRecycleInfoResp)
	err := c.cc.Invoke(ctx, DocumentLibrary_GetRecycleInfoByDistributeRecordFileId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *documentLibraryClient) GetRecycleInfoByDistributeId(ctx context.Context, in *GetRecycleInfoReq, opts ...grpc.CallOption) (*GetRecycleInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetRecycleInfoResp)
	err := c.cc.Invoke(ctx, DocumentLibrary_GetRecycleInfoByDistributeId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *documentLibraryClient) CreateBorrowRecord(ctx context.Context, in *BorrowRecordCreateReq, opts ...grpc.CallOption) (*BorrowRecordCreateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BorrowRecordCreateResp)
	err := c.cc.Invoke(ctx, DocumentLibrary_CreateBorrowRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *documentLibraryClient) UpdateUserDisposalStatus(ctx context.Context, in *UpdateUserDisposalStatusReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptyResp)
	err := c.cc.Invoke(ctx, DocumentLibrary_UpdateUserDisposalStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DocumentLibraryServer is the server API for DocumentLibrary service.
// All implementations must embed UnimplementedDocumentLibraryServer
// for forward compatibility.
type DocumentLibraryServer interface {
	// 保存发放记录
	PreSaveDistributeRecord(context.Context, *DocumentDistributeReq) (*EmptyResp, error)
	// 获取文档权限用户
	GetDocPermissionUsers(context.Context, *GetDocPermissionUsersReq) (*GetDocPermissionUsersResp, error)
	// 更新发放状态
	UpdateDistributeStatusByWorkflowId(context.Context, *UpdateDistributeStatusReq) (*EmptyResp, error)
	// 保存发放审批信息
	SaveDistributeApproval(context.Context, *DistributeApprovalReq) (*EmptyResp, error)
	// 保存回收审批信息
	SaveRecycleApprovalInfo(context.Context, *RecycleApprovalInfo) (*EmptyResp, error)
	// 保存处置审批信息
	SaveDisposalApprovalInfo(context.Context, *DisposalApprovalInfo) (*EmptyResp, error)
	// 获取发放信息列表
	GetDistributeInfoList(context.Context, *GetDistributeListReq) (*GetDistributeListResp, error)
	// 根据发放列表id获取发放清单信息
	GetDistributeApplicationById(context.Context, *GetDistributeApplicationReq) (*GetDistributeApplicationResp, error)
	// 删除发放记录
	DeleteDistributeRecord(context.Context, *DeleteDistributeReq) (*EmptyResp, error)
	// 获取发放回收详情
	GetDistributeDetail(context.Context, *GetDistributeDetailReq) (*GetDistributeDetailResp, error)
	// 根据发放清单ID查询回收信息
	GetRecycleInfoByDistributeRecordFileId(context.Context, *GetRecycleInfoReq) (*GetRecycleInfoResp, error)
	GetRecycleInfoByDistributeId(context.Context, *GetRecycleInfoReq) (*GetRecycleInfoResp, error)
	// 创建借阅记录
	CreateBorrowRecord(context.Context, *BorrowRecordCreateReq) (*BorrowRecordCreateResp, error)
	// 更新用户处置状态
	UpdateUserDisposalStatus(context.Context, *UpdateUserDisposalStatusReq) (*EmptyResp, error)
	mustEmbedUnimplementedDocumentLibraryServer()
}

// UnimplementedDocumentLibraryServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDocumentLibraryServer struct{}

func (UnimplementedDocumentLibraryServer) PreSaveDistributeRecord(context.Context, *DocumentDistributeReq) (*EmptyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreSaveDistributeRecord not implemented")
}
func (UnimplementedDocumentLibraryServer) GetDocPermissionUsers(context.Context, *GetDocPermissionUsersReq) (*GetDocPermissionUsersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDocPermissionUsers not implemented")
}
func (UnimplementedDocumentLibraryServer) UpdateDistributeStatusByWorkflowId(context.Context, *UpdateDistributeStatusReq) (*EmptyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDistributeStatusByWorkflowId not implemented")
}
func (UnimplementedDocumentLibraryServer) SaveDistributeApproval(context.Context, *DistributeApprovalReq) (*EmptyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveDistributeApproval not implemented")
}
func (UnimplementedDocumentLibraryServer) SaveRecycleApprovalInfo(context.Context, *RecycleApprovalInfo) (*EmptyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveRecycleApprovalInfo not implemented")
}
func (UnimplementedDocumentLibraryServer) SaveDisposalApprovalInfo(context.Context, *DisposalApprovalInfo) (*EmptyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveDisposalApprovalInfo not implemented")
}
func (UnimplementedDocumentLibraryServer) GetDistributeInfoList(context.Context, *GetDistributeListReq) (*GetDistributeListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDistributeInfoList not implemented")
}
func (UnimplementedDocumentLibraryServer) GetDistributeApplicationById(context.Context, *GetDistributeApplicationReq) (*GetDistributeApplicationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDistributeApplicationById not implemented")
}
func (UnimplementedDocumentLibraryServer) DeleteDistributeRecord(context.Context, *DeleteDistributeReq) (*EmptyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDistributeRecord not implemented")
}
func (UnimplementedDocumentLibraryServer) GetDistributeDetail(context.Context, *GetDistributeDetailReq) (*GetDistributeDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDistributeDetail not implemented")
}
func (UnimplementedDocumentLibraryServer) GetRecycleInfoByDistributeRecordFileId(context.Context, *GetRecycleInfoReq) (*GetRecycleInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecycleInfoByDistributeRecordFileId not implemented")
}
func (UnimplementedDocumentLibraryServer) GetRecycleInfoByDistributeId(context.Context, *GetRecycleInfoReq) (*GetRecycleInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecycleInfoByDistributeId not implemented")
}
func (UnimplementedDocumentLibraryServer) CreateBorrowRecord(context.Context, *BorrowRecordCreateReq) (*BorrowRecordCreateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBorrowRecord not implemented")
}
func (UnimplementedDocumentLibraryServer) UpdateUserDisposalStatus(context.Context, *UpdateUserDisposalStatusReq) (*EmptyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserDisposalStatus not implemented")
}
func (UnimplementedDocumentLibraryServer) mustEmbedUnimplementedDocumentLibraryServer() {}
func (UnimplementedDocumentLibraryServer) testEmbeddedByValue()                         {}

// UnsafeDocumentLibraryServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DocumentLibraryServer will
// result in compilation errors.
type UnsafeDocumentLibraryServer interface {
	mustEmbedUnimplementedDocumentLibraryServer()
}

func RegisterDocumentLibraryServer(s grpc.ServiceRegistrar, srv DocumentLibraryServer) {
	// If the following call pancis, it indicates UnimplementedDocumentLibraryServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DocumentLibrary_ServiceDesc, srv)
}

func _DocumentLibrary_PreSaveDistributeRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DocumentDistributeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocumentLibraryServer).PreSaveDistributeRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocumentLibrary_PreSaveDistributeRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocumentLibraryServer).PreSaveDistributeRecord(ctx, req.(*DocumentDistributeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DocumentLibrary_GetDocPermissionUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDocPermissionUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocumentLibraryServer).GetDocPermissionUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocumentLibrary_GetDocPermissionUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocumentLibraryServer).GetDocPermissionUsers(ctx, req.(*GetDocPermissionUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DocumentLibrary_UpdateDistributeStatusByWorkflowId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDistributeStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocumentLibraryServer).UpdateDistributeStatusByWorkflowId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocumentLibrary_UpdateDistributeStatusByWorkflowId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocumentLibraryServer).UpdateDistributeStatusByWorkflowId(ctx, req.(*UpdateDistributeStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DocumentLibrary_SaveDistributeApproval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DistributeApprovalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocumentLibraryServer).SaveDistributeApproval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocumentLibrary_SaveDistributeApproval_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocumentLibraryServer).SaveDistributeApproval(ctx, req.(*DistributeApprovalReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DocumentLibrary_SaveRecycleApprovalInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecycleApprovalInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocumentLibraryServer).SaveRecycleApprovalInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocumentLibrary_SaveRecycleApprovalInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocumentLibraryServer).SaveRecycleApprovalInfo(ctx, req.(*RecycleApprovalInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _DocumentLibrary_SaveDisposalApprovalInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisposalApprovalInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocumentLibraryServer).SaveDisposalApprovalInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocumentLibrary_SaveDisposalApprovalInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocumentLibraryServer).SaveDisposalApprovalInfo(ctx, req.(*DisposalApprovalInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _DocumentLibrary_GetDistributeInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDistributeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocumentLibraryServer).GetDistributeInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocumentLibrary_GetDistributeInfoList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocumentLibraryServer).GetDistributeInfoList(ctx, req.(*GetDistributeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DocumentLibrary_GetDistributeApplicationById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDistributeApplicationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocumentLibraryServer).GetDistributeApplicationById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocumentLibrary_GetDistributeApplicationById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocumentLibraryServer).GetDistributeApplicationById(ctx, req.(*GetDistributeApplicationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DocumentLibrary_DeleteDistributeRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDistributeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocumentLibraryServer).DeleteDistributeRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocumentLibrary_DeleteDistributeRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocumentLibraryServer).DeleteDistributeRecord(ctx, req.(*DeleteDistributeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DocumentLibrary_GetDistributeDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDistributeDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocumentLibraryServer).GetDistributeDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocumentLibrary_GetDistributeDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocumentLibraryServer).GetDistributeDetail(ctx, req.(*GetDistributeDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DocumentLibrary_GetRecycleInfoByDistributeRecordFileId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecycleInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocumentLibraryServer).GetRecycleInfoByDistributeRecordFileId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocumentLibrary_GetRecycleInfoByDistributeRecordFileId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocumentLibraryServer).GetRecycleInfoByDistributeRecordFileId(ctx, req.(*GetRecycleInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DocumentLibrary_GetRecycleInfoByDistributeId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecycleInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocumentLibraryServer).GetRecycleInfoByDistributeId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocumentLibrary_GetRecycleInfoByDistributeId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocumentLibraryServer).GetRecycleInfoByDistributeId(ctx, req.(*GetRecycleInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DocumentLibrary_CreateBorrowRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BorrowRecordCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocumentLibraryServer).CreateBorrowRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocumentLibrary_CreateBorrowRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocumentLibraryServer).CreateBorrowRecord(ctx, req.(*BorrowRecordCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DocumentLibrary_UpdateUserDisposalStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserDisposalStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocumentLibraryServer).UpdateUserDisposalStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocumentLibrary_UpdateUserDisposalStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocumentLibraryServer).UpdateUserDisposalStatus(ctx, req.(*UpdateUserDisposalStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DocumentLibrary_ServiceDesc is the grpc.ServiceDesc for DocumentLibrary service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DocumentLibrary_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "docvault.DocumentLibrary",
	HandlerType: (*DocumentLibraryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PreSaveDistributeRecord",
			Handler:    _DocumentLibrary_PreSaveDistributeRecord_Handler,
		},
		{
			MethodName: "GetDocPermissionUsers",
			Handler:    _DocumentLibrary_GetDocPermissionUsers_Handler,
		},
		{
			MethodName: "UpdateDistributeStatusByWorkflowId",
			Handler:    _DocumentLibrary_UpdateDistributeStatusByWorkflowId_Handler,
		},
		{
			MethodName: "SaveDistributeApproval",
			Handler:    _DocumentLibrary_SaveDistributeApproval_Handler,
		},
		{
			MethodName: "SaveRecycleApprovalInfo",
			Handler:    _DocumentLibrary_SaveRecycleApprovalInfo_Handler,
		},
		{
			MethodName: "SaveDisposalApprovalInfo",
			Handler:    _DocumentLibrary_SaveDisposalApprovalInfo_Handler,
		},
		{
			MethodName: "GetDistributeInfoList",
			Handler:    _DocumentLibrary_GetDistributeInfoList_Handler,
		},
		{
			MethodName: "GetDistributeApplicationById",
			Handler:    _DocumentLibrary_GetDistributeApplicationById_Handler,
		},
		{
			MethodName: "DeleteDistributeRecord",
			Handler:    _DocumentLibrary_DeleteDistributeRecord_Handler,
		},
		{
			MethodName: "GetDistributeDetail",
			Handler:    _DocumentLibrary_GetDistributeDetail_Handler,
		},
		{
			MethodName: "GetRecycleInfoByDistributeRecordFileId",
			Handler:    _DocumentLibrary_GetRecycleInfoByDistributeRecordFileId_Handler,
		},
		{
			MethodName: "GetRecycleInfoByDistributeId",
			Handler:    _DocumentLibrary_GetRecycleInfoByDistributeId_Handler,
		},
		{
			MethodName: "CreateBorrowRecord",
			Handler:    _DocumentLibrary_CreateBorrowRecord_Handler,
		},
		{
			MethodName: "UpdateUserDisposalStatus",
			Handler:    _DocumentLibrary_UpdateUserDisposalStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rpc/docvault.proto",
}
