package documentlibrarylogic

import (
	"context"

	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDistributeRecordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteDistributeRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDistributeRecordLogic {
	return &DeleteDistributeRecordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除发放记录
func (l *DeleteDistributeRecordLogic) DeleteDistributeRecord(in *docvault.DeleteDistributeReq) (*docvault.EmptyResp, error) {
	err := l.svcCtx.Factory.NewDocumentLibrary().DeleteDistributeRecord(l.ctx, in.Id)
	return &docvault.EmptyResp{}, err
}
