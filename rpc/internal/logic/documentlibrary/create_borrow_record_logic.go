package documentlibrarylogic

import (
	"context"
	"errors"
	"time"

	"docvault/rpc/internal/domain/root"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"
	"docvault/utils"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

type CreateBorrowRecordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateBorrowRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBorrowRecordLogic {
	return &CreateBorrowRecordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// CreateBorrowRecord 创建借阅记录
// 功能: 接收借阅记录创建请求，转换为领域实体并调用服务层创建借阅记录
// 参数:
//
//	in: 借阅记录创建请求
//
// 返回值:
//
//	*docvault.BorrowRecordCreateResp: 创建响应，包含借阅记录ID
//	error: 错误信息，成功时为nil
//
// 异常: 参数验证异常、业务逻辑异常、数据库操作异常
func (l *CreateBorrowRecordLogic) CreateBorrowRecord(in *docvault.BorrowRecordCreateReq) (*docvault.BorrowRecordCreateResp, error) {
	// 实现步骤:
	// 1. 参数验证
	// 2. 转换proto请求为领域实体
	// 3. 调用BorrowRecordService创建记录
	// 4. 返回响应

	// 1. 参数验证
	if in.UserId == "" {
		logc.Errorw(l.ctx, "用户ID不能为空", logc.Field("request", in))
		return nil, errors.New("用户ID不能为空")
	}

	if len(in.Documents) == 0 {
		logc.Errorw(l.ctx, "借阅文档列表不能为空", logc.Field("request", in))
		return nil, errors.New("借阅文档列表不能为空")
	}

	if in.DueTime <= 0 {
		logc.Errorw(l.ctx, "应还时间不能为空", logc.Field("request", in))
		return nil, errors.New("应还时间不能为空")
	}

	// 2. 转换proto请求为领域实体

	// 转换借阅文档列表
	documents, err := utils.ArrayCopy(l.ctx, root.BorrowDocumentItem{}, in.Documents)
	if err != nil {
		logc.Errorw(l.ctx, "转换借阅文档列表失败", logc.Field("error", err), logc.Field("documents", in.Documents))
		return nil, err
	}
	// 生成借阅记录ID
	borrowRecordID := l.svcCtx.IDGenerator.GenerateIDString()
	// 创建暂存状态的借阅记录
	borrowRecord := root.NewBorrowRecordDraft(
		borrowRecordID,
		in.UserId,
		documents,
		time.UnixMilli(in.BorrowTime),
		time.UnixMilli(in.DueTime),
		in.BorrowReason,
	)

	// 3. 调用BorrowRecordService创建记录
	borrowRecordService := l.svcCtx.Factory.NewBorrowRecordService()
	if err = borrowRecordService.CreateBorrowRecord(*borrowRecord); err != nil {
		logc.Errorw(l.ctx, "创建借阅记录失败", logc.Field("error", err), logc.Field("borrowRecord", borrowRecord))
		return nil, err
	}

	logc.Infow(l.ctx, "创建借阅记录成功", logc.Field("borrowRecordId", borrowRecordID))

	// 4. 返回响应
	return &docvault.BorrowRecordCreateResp{
		Id: borrowRecordID,
	}, nil
}
