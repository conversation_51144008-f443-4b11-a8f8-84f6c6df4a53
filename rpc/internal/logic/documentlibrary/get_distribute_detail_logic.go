package documentlibrarylogic

import (
	"context"
	"fmt"

	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDistributeDetailLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetDistributeDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDistributeDetailLogic {
	return &GetDistributeDetailLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// GetDistributeDetail 获取发放回收详情
// 根据发放记录ID查询详细的发放信息，包括发放清单和权限详情
// 参数:
//   - in: 包含发放记录ID的请求
//
// 返回:
//   - *docvault.GetDistributeDetailResp: 发放详情响应
//   - error: 错误信息
func (l *GetDistributeDetailLogic) GetDistributeDetail(in *docvault.GetDistributeDetailReq) (*docvault.GetDistributeDetailResp, error) {
	// 查询发放记录基本信息
	distributeRecord, err := l.getDistributeRecord(in.Id)
	if err != nil {
		l.Errorf("查询发放记录失败: %v", err)
		return nil, err
	}

	// 查询发放清单详情
	distributeList, err := l.getDistributeList(in.Id)
	if err != nil {
		l.Errorf("查询发放清单失败: %v", err)
		return nil, err
	}

	// 构建响应
	detail := &docvault.DistributeDetailInfo{
		Id:                 distributeRecord.ID,
		WorkflowId:         distributeRecord.WorkflowID,
		Applicant:          distributeRecord.Applicant,
		ApplicantName:      l.getUserName(distributeRecord.Applicant),
		ApplyDate:          distributeRecord.ApplyDate,
		DistributeType:     distributeRecord.DistributeType,
		FileType:           distributeRecord.FileType,
		FileCategory:       distributeRecord.FileCategory,
		TypeDictNodeId:     distributeRecord.TypeDictNodeId,
		Reason:             distributeRecord.Reason,
		OtherReason:        distributeRecord.OtherReason,
		WishDistributeDate: distributeRecord.WishDistributeDate,
		Status:             int32(distributeRecord.Status),
		DistributeList:     distributeList,
		CreatedAt:          distributeRecord.CreatedAt.Unix(),
		UpdatedAt:          distributeRecord.UpdatedAt.Unix(),
	}

	return &docvault.GetDistributeDetailResp{
		Detail: detail,
	}, nil
}

// getDistributeRecord 查询发放记录基本信息
// 参数:
//   - id: 发放记录ID
//
// 返回:
//   - *mapper.DistributeRecord: 发放记录信息
//   - error: 错误信息
func (l *GetDistributeDetailLogic) getDistributeRecord(id string) (*mapper.DistributeRecord, error) {
	// 调用mapper层查询发放记录
	client := mapper.NewDistributeRecordClient(l.svcCtx.DocvaultDB)
	return client.GetByID(l.ctx, id)
}

// getDistributeList 查询发放清单详情
// 参数:
//   - distributeId: 发放记录ID
//
// 返回:
//   - []*docvault.DistributeInventoryDetail: 发放清单列表
//   - error: 错误信息
func (l *GetDistributeDetailLogic) getDistributeList(distributeId string) ([]*docvault.DistributeInventoryDetail, error) {
	// 1. 查询发放文件清单
	files, err := l.queryDistributeFiles(distributeId)
	if err != nil {
		return nil, err
	}

	if len(files) == 0 {
		return []*docvault.DistributeInventoryDetail{}, nil
	}

	// 2. 批量查询权限数据
	allPermissions, err := l.queryFilePermissions(files)
	if err != nil {
		return nil, err
	}

	// 3. 批量获取用户相关信息
	userNicknameMap, userRecycleMap, err := l.batchQueryUserInfo(distributeId, allPermissions)
	if err != nil {
		return nil, err
	}

	// 4. 组装返回数据
	return l.buildInventoryDetails(files, allPermissions, userNicknameMap, userRecycleMap), nil
}

// queryDistributeFiles 查询发放文件清单
// 参数:
//   - distributeId: 发放记录ID
//
// 返回:
//   - []*mapper.DistributeRecordFile: 文件清单列表
//   - error: 错误信息
func (l *GetDistributeDetailLogic) queryDistributeFiles(distributeId string) ([]*mapper.DistributeRecordFile, error) {
	fileClient := mapper.NewDistributeRecordFileClient(l.svcCtx.DocvaultDB)
	files, err := fileClient.GetByRecordID(l.ctx, distributeId)
	if err != nil {
		return nil, fmt.Errorf("查询发放文件清单失败: %v", err)
	}
	return files, nil
}

// queryFilePermissions 批量查询文件权限详情
// 参数:
//   - files: 文件清单列表
//
// 返回:
//   - []*mapper.DistributeRecordPermission: 权限详情列表
//   - error: 错误信息
func (l *GetDistributeDetailLogic) queryFilePermissions(files []*mapper.DistributeRecordFile) ([]*mapper.DistributeRecordPermission, error) {
	fileRecordIDs := make([]string, len(files))
	for i, file := range files {
		fileRecordIDs[i] = file.ID
	}

	permissionClient := mapper.NewDistributeRecordPermissionClient(l.svcCtx.DocvaultDB)
	allPermissions, err := permissionClient.GetPermissionsByFileRecordIDs(l.ctx, fileRecordIDs)
	if err != nil {
		return nil, fmt.Errorf("批量查询文件权限失败: %v", err)
	}
	return allPermissions, nil
}

// batchQueryUserInfo 批量查询用户相关信息
// 参数:
//   - distributeId: 发放记录ID
//   - allPermissions: 所有权限详情
//
// 返回:
//   - map[string]string: 用户昵称映射
//   - map[string]*mapper.UserRecycleInfo: 用户回收状态映射
//   - error: 错误信息
func (l *GetDistributeDetailLogic) batchQueryUserInfo(distributeId string, allPermissions []*mapper.DistributeRecordPermission) (map[string]string, map[string]*mapper.UserRecycleInfo, error) {
	// 收集所有唯一的用户ID
	userIDSet := make(map[string]bool)
	for _, perm := range allPermissions {
		userIDSet[perm.UserID] = true
	}

	userIDs := make([]string, 0, len(userIDSet))
	for userID := range userIDSet {
		userIDs = append(userIDs, userID)
	}

	// 批量获取用户昵称
	userNicknameMap, err := l.batchGetUserNames(userIDs)
	if err != nil {
		return nil, nil, fmt.Errorf("批量获取用户昵称失败: %v", err)
	}

	// 批量获取用户回收状态
	recycleClient := mapper.NewRecycleRecordPermissionClient(l.svcCtx.DocvaultDB)
	userRecycleMap, err := recycleClient.GetUserRecycleStatus(l.ctx, distributeId, userIDs)
	if err != nil {
		return nil, nil, fmt.Errorf("批量获取用户回收状态失败: %v", err)
	}

	return userNicknameMap, userRecycleMap, nil
}

// buildInventoryDetails 组装发放清单详情
// 参数:
//   - files: 文件清单列表
//   - allPermissions: 所有权限详情
//   - userNicknameMap: 用户昵称映射
//   - userRecycleMap: 用户回收状态映射
//
// 返回:
//   - []*docvault.DistributeInventoryDetail: 发放清单详情列表
func (l *GetDistributeDetailLogic) buildInventoryDetails(
	files []*mapper.DistributeRecordFile,
	allPermissions []*mapper.DistributeRecordPermission,
	userNicknameMap map[string]string,
	userRecycleMap map[string]*mapper.UserRecycleInfo,
) []*docvault.DistributeInventoryDetail {
	// 按文件ID分组权限数据
	permissionsByFileID := l.groupPermissionsByFileID(allPermissions)

	var result []*docvault.DistributeInventoryDetail
	for _, file := range files {
		permissions := permissionsByFileID[file.ID]
		permissionDetails := l.buildPermissionDetails(permissions, userNicknameMap, userRecycleMap)

		inventoryDetail := &docvault.DistributeInventoryDetail{
			Id:          file.ID,
			FileId:      file.FileID,
			FileName:    file.FileName,
			Number:      file.Number,
			Version:     file.Version,
			Permissions: permissionDetails,
		}

		result = append(result, inventoryDetail)
	}

	return result
}

// buildPermissionDetails 构建权限详情列表
// 参数:
//   - permissions: 权限列表
//   - userNicknameMap: 用户昵称映射
//   - userRecycleMap: 用户回收状态映射
//
// 返回:
//   - []*docvault.PermissionDetail: 权限详情列表
func (l *GetDistributeDetailLogic) buildPermissionDetails(
	permissions []*mapper.DistributeRecordPermission,
	userNicknameMap map[string]string,
	userRecycleMap map[string]*mapper.UserRecycleInfo,
) []*docvault.PermissionDetail {
	permissionMap := make(map[string]*docvault.PermissionDetail)

	for _, perm := range permissions {
		// 创建权限组合的唯一键
		key := fmt.Sprintf("%d-%d-%s", perm.FileForm, perm.FilePermission, perm.Recipient)

		userDetail := l.buildUserDetail(perm.UserID, userNicknameMap, userRecycleMap)

		if permDetail, exists := permissionMap[key]; exists {
			// 如果权限组合已存在，添加用户到接收人列表
			permDetail.ReceivedBy = append(permDetail.ReceivedBy, userDetail)
		} else {
			// 创建新的权限详情
			permissionMap[key] = &docvault.PermissionDetail{
				FileForm:       perm.FileForm,
				FilePermission: perm.FilePermission,
				Recipient:      perm.Recipient,
				ReceivedBy:     []*docvault.DistributeUserDetail{userDetail},
			}
		}
	}

	// 将map转换为slice
	var permissionDetails []*docvault.PermissionDetail
	for _, permDetail := range permissionMap {
		permissionDetails = append(permissionDetails, permDetail)
	}

	return permissionDetails
}

// buildUserDetail 构建用户详情
// 参数:
//   - userID: 用户ID
//   - userNicknameMap: 用户昵称映射
//   - userRecycleMap: 用户回收状态映射
//
// 返回:
//   - *docvault.DistributeUserDetail: 用户详情
func (l *GetDistributeDetailLogic) buildUserDetail(
	userID string,
	userNicknameMap map[string]string,
	userRecycleMap map[string]*mapper.UserRecycleInfo,
) *docvault.DistributeUserDetail {
	userNickname := userNicknameMap[userID]
	if userNickname == "" {
		userNickname = userID // 降级处理
	}

	// 获取用户回收状态信息
	recycleInfo := userRecycleMap[userID]
	recycleStatus := int32(0) // 默认未回收
	recycleTime := int64(0)
	if recycleInfo != nil {
		recycleStatus = recycleInfo.RecycleStatus
		recycleTime = recycleInfo.RecycleTime
	}

	return &docvault.DistributeUserDetail{
		UserId:        userID,
		UserNickname:  userNickname,
		RecycleStatus: recycleStatus,
		RecycleTime:   recycleTime,
	}
}

// groupPermissionsByFileID 按文件ID分组权限数据
// 参数:
//   - allPermissions: 所有权限详情
//
// 返回:
//   - map[string][]*mapper.DistributeRecordPermission: 按文件ID分组的权限映射
func (l *GetDistributeDetailLogic) groupPermissionsByFileID(allPermissions []*mapper.DistributeRecordPermission) map[string][]*mapper.DistributeRecordPermission {
	permissionsByFileID := make(map[string][]*mapper.DistributeRecordPermission)
	for _, perm := range allPermissions {
		permissionsByFileID[perm.FileRecordID] = append(permissionsByFileID[perm.FileRecordID], perm)
	}
	return permissionsByFileID
}

// getUserName 获取用户姓名
// 参数:
//   - userId: 用户ID
//
// 返回:
//   - string: 用户姓名
func (l *GetDistributeDetailLogic) getUserName(userId string) string {
	return l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, userId)
}

// batchGetUserNames 批量获取用户昵称
// 参数:
//   - userIDs: 用户ID列表
//
// 返回:
//   - map[string]string: 用户ID到昵称的映射
//   - error: 错误信息
func (l *GetDistributeDetailLogic) batchGetUserNames(userIDs []string) (map[string]string, error) {
	userNicknameMap := make(map[string]string)

	// 逐个获取用户昵称
	for _, userID := range userIDs {
		nickname := l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, userID)
		userNicknameMap[userID] = nickname
	}

	return userNicknameMap, nil
}
