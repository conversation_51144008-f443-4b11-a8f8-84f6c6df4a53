package documentlibrarylogic

import (
	"context"
	"encoding/json"
	"errors"

	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

// 错误定义
var (
	ErrInvalidParam   = errors.New("参数无效")
	ErrRecordNotFound = errors.New("记录不存在")
)

type GetRecycleInfoByDistributeRecordFileIdLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetRecycleInfoByDistributeRecordFileIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetRecycleInfoByDistributeRecordFileIdLogic {
	return &GetRecycleInfoByDistributeRecordFileIdLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// GetRecycleInfoByDistributeRecordFileId 根据发放记录文件ID查询回收信息
// 参数:
//   - in: 查询请求，包含发放记录文件ID
//
// 返回:
//   - *docvault.GetRecycleInfoResp: 回收信息响应，包含文件名称、文件编号和回收记录列表
//   - error: 错误信息，成功时返回nil
//
// 业务逻辑:
//  1. 参数校验：检查发放记录文件ID是否有效
//  2. 查询文件信息：根据发放记录文件ID获取文件名称和编号
//  3. 查询回收记录：根据发放记录文件ID获取所有相关的回收记录
//  4. 组装回收信息：为每条回收记录查询交还人、审批人等详细信息
//  5. 返回完整的回收信息
func (l *GetRecycleInfoByDistributeRecordFileIdLogic) GetRecycleInfoByDistributeRecordFileId(in *docvault.GetRecycleInfoReq) (*docvault.GetRecycleInfoResp, error) {
	// 参数校验
	if in.DistributeRecordFileId == "" {
		l.Logger.Error("发放记录文件ID不能为空")
		return nil, ErrInvalidParam
	}

	// 查询回收信息
	recycleInfo, err := l.queryRecycleInfo(in.DistributeRecordFileId)
	if err != nil {
		l.Logger.Errorf("查询回收信息失败: %v", err)
		return nil, err
	}

	return &docvault.GetRecycleInfoResp{
		RecycleInfo: recycleInfo,
	}, nil
}

// queryRecycleInfo 查询回收信息
// 参数:
//   - distributeRecordFileId: 发放记录文件ID
//
// 返回:
//   - *docvault.RecycleInfo: 回收信息，包含文件名称、文件编号和回收记录列表
//   - error: 错误信息，成功时返回nil
//
// 业务逻辑:
//  1. 查询发放记录文件信息获取文件名称和编号
//  2. 查询所有相关的回收记录
//  3. 组装完整的回收信息
func (l *GetRecycleInfoByDistributeRecordFileIdLogic) queryRecycleInfo(distributeRecordFileId string) (*docvault.RecycleInfo, error) {
	// 1. 查询发放记录文件信息（文件名称、文件编号）
	fileInfo, err := l.queryFileInfo(distributeRecordFileId)
	if err != nil {
		return nil, err
	}

	// 2. 查询回收记录列表
	recycleRecords, err := l.queryRecycleRecords(distributeRecordFileId)
	if err != nil {
		return nil, err
	}

	return &docvault.RecycleInfo{
		FileName:       fileInfo.FileName,
		FileNumber:     fileInfo.FileNumber,
		RecycleRecords: recycleRecords,
	}, nil
}

// FileInfo 文件基本信息
type FileInfo struct {
	FileName   string // 文件名称
	FileNumber string // 文件编号
}

// queryFileInfo 查询文件基本信息
// 参数:
//   - distributeRecordFileId: 发放记录文件ID
//
// 返回:
//   - *FileInfo: 文件基本信息，包含文件名称和编号
//   - error: 错误信息，成功时返回nil
//
// 业务逻辑:
//
//	通过发放记录文件ID直接查询发放文件记录，获取文件名称和编号
func (l *GetRecycleInfoByDistributeRecordFileIdLogic) queryFileInfo(distributeRecordFileId string) (*FileInfo, error) {
	// 直接通过发放记录文件ID查询发放文件记录
	distributeFileClient := mapper.NewDistributeRecordFileClient(l.svcCtx.DocvaultDB)
	fileRecord, err := distributeFileClient.GetByID(l.ctx, distributeRecordFileId)
	if err != nil {
		l.Logger.Errorf("查询发放文件记录失败: %v", err)
		return nil, err
	}

	if fileRecord == nil {
		l.Logger.Error("发放文件记录不存在")
		return nil, ErrRecordNotFound
	}

	// 构建文件信息
	return &FileInfo{
		FileName:   fileRecord.FileName, // 使用文件名称
		FileNumber: fileRecord.Number,   // 使用文件编号
	}, nil
}

// queryRecycleRecords 查询回收记录列表
// 参数:
//   - distributeRecordFileId: 发放记录文件ID
//
// 返回:
//   - []*docvault.RecycleRecord: 回收记录列表
//   - error: 错误信息，成功时返回nil
//
// 业务逻辑:
//  1. 根据发放记录文件ID查询所有相关的回收记录
//  2. 批量查询所有回收记录的交还人信息（避免N+1查询）
//  3. 解析审批信息获取审批人和批准人
//  4. 组装完整的回收记录信息
func (l *GetRecycleInfoByDistributeRecordFileIdLogic) queryRecycleRecords(distributeRecordFileId string) ([]*docvault.RecycleRecord, error) {
	// 1. 先通过发放记录文件ID查询回收记录文件列表
	recycleFileClient := mapper.NewRecycleRecordFileClient(l.svcCtx.DocvaultDB)
	recycleFiles, err := recycleFileClient.GetByDistributeRecordFileID(l.ctx, distributeRecordFileId)
	if err != nil {
		l.Logger.Errorf("查询回收记录文件失败: %v", err)
		return nil, err
	}

	if len(recycleFiles) == 0 {
		return []*docvault.RecycleRecord{}, nil
	}

	// 2. 收集所有回收记录ID
	recordIDSet := make(map[string]bool)
	for _, file := range recycleFiles {
		recordIDSet[file.RecordID] = true
	}

	// 3. 根据回收记录ID查询回收记录
	recycleRecordClient := mapper.NewRecycleRecordClient(l.svcCtx.DocvaultDB)
	var recycleRecords []*mapper.RecycleRecord
	for recordID := range recordIDSet {
		record, err := recycleRecordClient.GetByID(l.ctx, recordID)
		if err != nil {
			l.Logger.Errorf("查询回收记录失败, recordID: %s, error: %v", recordID, err)
			continue
		}
		if record != nil {
			recycleRecords = append(recycleRecords, record)
		}
	}

	if len(recycleRecords) == 0 {
		return []*docvault.RecycleRecord{}, nil
	}

	// 2. 批量查询所有回收记录的交还人信息（性能优化：避免N+1查询）
	handoverPersonsMap, err := l.batchQueryHandoverPersons(distributeRecordFileId, recycleRecords)
	if err != nil {
		l.Logger.Errorf("批量查询交还人信息失败: %v", err)
		return nil, err
	}

	// 3. 组装回收记录信息
	var result []*docvault.RecycleRecord
	for _, record := range recycleRecords {
		// 获取交还人信息
		handoverPersons := handoverPersonsMap[record.ID]

		// 解析审批信息（直接从Redis获取用户姓名）
		auditors, approvers, err := l.parseApprovalInfo(record.ApprovalInfo)
		if err != nil {
			l.Logger.Errorf("解析审批信息失败: %v", err)
			// 审批信息解析失败不影响主流程，使用空列表
			auditors = []string{}
			approvers = []string{}
		}

		// 获取回收发起人姓名（直接从Redis获取）
		initiatorName := l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, record.RecycleBy)
		if initiatorName == "" {
			initiatorName = record.RecycleBy // 使用ID作为备用
		}

		recycleRecordProto := &docvault.RecycleRecord{
			RecycleInitiator: initiatorName,
			RecycleReason:    record.RecycleReason,
			HandoverPersons:  handoverPersons,
			Auditors:         auditors,
			Approvers:        approvers,
			RecycleDate:      record.RecycleDate.UnixMilli(), // 转换为毫秒级时间戳
		}

		result = append(result, recycleRecordProto)
	}

	return result, nil
}

// batchQueryHandoverPersons 批量查询交还人信息
// 参数:
//   - distributeRecordFileId: 发放记录文件ID
//
// 返回:
//   - map[string][]*docvault.HandoverPerson: 回收记录ID到交还人信息的映射
//   - error: 错误信息，成功时返回nil
//
// 业务逻辑:
//  1. 根据发放记录文件ID查询回收文件记录列表
//  2. 提取回收文件记录的ID列表
//  3. 根据文件记录ID批量查询权限信息
//  4. 组装交还人信息并按回收记录ID分组
func (l *GetRecycleInfoByDistributeRecordFileIdLogic) batchQueryHandoverPersons(distributeRecordFileId string, recycleRecords []*mapper.RecycleRecord) (map[string][]*docvault.HandoverPerson, error) {
	if len(recycleRecords) == 0 {
		return make(map[string][]*docvault.HandoverPerson), nil
	}

	// 1. 根据发放记录文件ID查询回收文件记录列表
	recycleFileClient := mapper.NewRecycleRecordFileClient(l.svcCtx.DocvaultDB)
	recycleFiles, err := recycleFileClient.GetByDistributeRecordFileID(l.ctx, distributeRecordFileId)
	if err != nil {
		l.Logger.Errorf("查询回收文件记录失败: %v", err)
		return nil, err
	}

	if len(recycleFiles) == 0 {
		// 没有回收文件记录，返回空的交还人信息
		result := make(map[string][]*docvault.HandoverPerson)
		for _, record := range recycleRecords {
			result[record.ID] = []*docvault.HandoverPerson{}
		}
		return result, nil
	}

	// 2. 提取回收文件记录的ID列表
	fileRecordIDs := make([]string, 0, len(recycleFiles))
	for _, file := range recycleFiles {
		fileRecordIDs = append(fileRecordIDs, file.ID)
	}

	// 3. 根据文件记录ID批量查询权限信息
	recyclePermissionClient := mapper.NewRecycleRecordPermissionClient(l.svcCtx.DocvaultDB)
	allPermissions, err := l.batchGetPermissionsByFileRecordIDs(recyclePermissionClient, fileRecordIDs)
	if err != nil {
		l.Logger.Errorf("批量查询回收权限记录失败: %v", err)
		return nil, err
	}

	// 4. 构建文件ID到文件信息的映射
	fileMap := make(map[string]*mapper.RecycleRecordFile)
	for _, file := range recycleFiles {
		fileMap[file.ID] = file
	}

	// 5. 构建文件ID到权限列表的映射
	filePermissionMap := make(map[string][]*mapper.RecycleRecordPermission)
	for _, permission := range allPermissions {
		filePermissionMap[permission.FileRecordID] = append(filePermissionMap[permission.FileRecordID], permission)
	}

	// 6. 构建回收记录ID到文件列表的映射
	recordFileMap := make(map[string][]*mapper.RecycleRecordFile)
	for _, file := range recycleFiles {
		recordFileMap[file.RecordID] = append(recordFileMap[file.RecordID], file)
	}

	// 7. 组装交还人信息并按回收记录ID分组
	result := make(map[string][]*docvault.HandoverPerson)
	for _, record := range recycleRecords {
		var handoverPersons []*docvault.HandoverPerson

		files := recordFileMap[record.ID]
		for _, file := range files {
			permissions := filePermissionMap[file.ID]
			for _, permission := range permissions {
				// 直接从Redis获取用户姓名（Redis性能很好，无需批量优化）
				handoverPerson := &docvault.HandoverPerson{
					HandoverId:     permission.UserID,
					HandoverName:   l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, permission.UserID),
					FileForm:       permission.FileForm,       // 从权限记录中获取文件形式
					FilePermission: permission.FilePermission, // 从权限记录中获取文件权限
				}

				handoverPersons = append(handoverPersons, handoverPerson)
			}
		}

		result[record.ID] = handoverPersons
	}

	return result, nil
}

// batchGetPermissionsByFileRecordIDs 批量根据文件记录ID查询权限信息
// 参数:
//   - client: 回收权限记录客户端
//   - fileRecordIDs: 文件记录ID列表
//
// 返回:
//   - []*mapper.RecycleRecordPermission: 权限记录列表
//   - error: 错误信息，成功时返回nil
//
// 业务逻辑:
//
//	使用IN查询批量获取权限记录，避免循环查询
func (l *GetRecycleInfoByDistributeRecordFileIdLogic) batchGetPermissionsByFileRecordIDs(client *mapper.RecycleRecordPermissionClient, fileRecordIDs []string) ([]*mapper.RecycleRecordPermission, error) {
	if len(fileRecordIDs) == 0 {
		return []*mapper.RecycleRecordPermission{}, nil
	}

	// 这里需要在mapper层添加批量查询方法，暂时使用循环查询
	// TODO: 在mapper层添加 GetByFileRecordIDs 方法进行真正的批量查询
	var allPermissions []*mapper.RecycleRecordPermission
	for _, fileRecordID := range fileRecordIDs {
		permissions, err := client.GetByFileRecordID(l.ctx, fileRecordID)
		if err != nil {
			l.Logger.Errorf("查询回收权限记录失败, fileRecordID: %s, error: %v", fileRecordID, err)
			continue // 单个查询失败不影响整体
		}
		allPermissions = append(allPermissions, permissions...)
	}

	return allPermissions, nil
}

// parseApprovalInfo 解析审批信息（直接从Redis获取用户姓名）
// 参数:
//   - approvalInfoJSON: 审批信息JSON数据
//
// 返回:
//   - []string: 审批人姓名列表
//   - []string: 批准人姓名列表
//   - error: 错误信息，成功时返回nil
//
// 业务逻辑:
//
//	解析JSON格式的审批信息，直接从Redis获取用户姓名（Redis性能很好，无需批量优化）
func (l *GetRecycleInfoByDistributeRecordFileIdLogic) parseApprovalInfo(approvalInfoJSON []byte) ([]string, []string, error) {
	if len(approvalInfoJSON) == 0 {
		return []string{}, []string{}, nil
	}

	// 定义审批信息结构
	type ApprovalItem struct {
		UserID     string `json:"userId"`
		PassedDate int64  `json:"passedDate"`
	}

	type ApprovalInfo struct {
		Auditors  []ApprovalItem `json:"auditors"`  // 审核人
		Approvers []ApprovalItem `json:"approvers"` // 批准人
	}

	var approvalInfo ApprovalInfo
	if err := json.Unmarshal(approvalInfoJSON, &approvalInfo); err != nil {
		l.Logger.Errorf("解析审批信息JSON失败: %v", err)
		return []string{}, []string{}, err
	}

	// 提取审批人姓名（直接从Redis获取）
	auditors := make([]string, 0, len(approvalInfo.Auditors))
	for _, auditor := range approvalInfo.Auditors {
		auditorName := l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, auditor.UserID)
		if auditorName == "" {
			auditorName = auditor.UserID // 使用ID作为备用
		}
		auditors = append(auditors, auditorName)
	}

	approvers := make([]string, 0, len(approvalInfo.Approvers))
	for _, approver := range approvalInfo.Approvers {
		approverName := l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, approver.UserID)
		if approverName == "" {
			approverName = approver.UserID // 使用ID作为备用
		}
		approvers = append(approvers, approverName)
	}

	return auditors, approvers, nil
}
