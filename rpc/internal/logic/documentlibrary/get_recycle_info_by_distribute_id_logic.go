package documentlibrarylogic

import (
	"context"

	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetRecycleInfoByDistributeIdLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetRecycleInfoByDistributeIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetRecycleInfoByDistributeIdLogic {
	return &GetRecycleInfoByDistributeIdLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetRecycleInfoByDistributeIdLogic) GetRecycleInfoByDistributeId(in *docvault.GetRecycleInfoReq) (*docvault.GetRecycleInfoResp, error) {
	// todo: add your logic here and delete this line

	return &docvault.GetRecycleInfoResp{}, nil
}
