package documentlibrarylogic

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"

	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateUserDisposalStatusLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateUserDisposalStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateUserDisposalStatusLogic {
	return &UpdateUserDisposalStatusLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 更新用户处置状态
func (l *UpdateUserDisposalStatusLogic) UpdateUserDisposalStatus(in *docvault.UpdateUserDisposalStatusReq) (*docvault.EmptyResp, error) {
	// 封装数据
	var updateUserDisposalStatus aggregate.UpdateUserDisposalStatusRequest
	var recycles []aggregate.RecycleList
	for _, recycle := range in.Recycles {
		var permissions []aggregate.FilePermission
		for _, permission := range recycle.Permissions {
			permissions = append(permissions, aggregate.FilePermission{
				FileForm:       int(permission.FileForm),
				FilePermission: int(permission.FilePermission),
				ReceivedBy:     permission.ReceivedBy,
			})
		}
		recycles = append(recycles, aggregate.RecycleList{
			InventoryID: recycle.InventoryId,
			Permissions: permissions,
		})
	}
	updateUserDisposalStatus.DistributeID = in.DistributeId
	updateUserDisposalStatus.Recycles = recycles
	updateUserDisposalStatus.DisposalStatus = in.DisposalStatus

	// 调用领域服务
	err := l.svcCtx.Factory.NewDocumentLibrary().UpdateUserDisposalStatus(l.ctx, updateUserDisposalStatus)
	if err != nil {
		l.Logger.Errorf("更新发放列表清单相关用户处置状态失败：%v", err)
		return nil, err
	}
	return &docvault.EmptyResp{}, nil
}
