package documentlibrarylogic

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/utils"

	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type SaveDisposalApprovalInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSaveDisposalApprovalInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SaveDisposalApprovalInfoLogic {
	return &SaveDisposalApprovalInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 保存处置审批信息
func (l *SaveDisposalApprovalInfoLogic) SaveDisposalApprovalInfo(in *docvault.DisposalApprovalInfo) (*docvault.EmptyResp, error) {
	request := l.dataConversion(in)

	request.DistributeID = in.DistributeId
	request.WorkflowID = in.WorkflowId
	request.DisposalBy = utils.GetContextUserID(l.ctx)
	err := l.svcCtx.Factory.NewDocumentLibrary().SaveDisposalApprovalInfo(l.ctx, request)
	if err != nil {
		l.Logger.Errorf("保存处置信息失败: %v", err)
		return nil, err
	}
	return &docvault.EmptyResp{}, nil
}

func (l *SaveDisposalApprovalInfoLogic) dataConversion(in *docvault.DisposalApprovalInfo) *aggregate.DisposalApprovalRequest {
	if in == nil {
		return nil
	}
	request := &aggregate.DisposalApprovalRequest{
		DistributeID:   in.DistributeId,
		DisposalBy:     utils.GetContextUserID(l.ctx),
		DisposalDate:   in.DisposalDate,
		DisposalReason: in.DisposalReason,
		WorkflowID:     in.WorkflowId,
	}
	var auditors []aggregate.ApprovalInfoItem
	for _, v := range in.ApprovalInfo.Auditors {
		auditors = append(auditors, aggregate.ApprovalInfoItem{
			UserID:     v.UserId,
			PassedDate: v.PassedDate,
		})
	}
	var approvers []aggregate.ApprovalInfoItem
	for _, v := range in.ApprovalInfo.Approvers {
		approvers = append(approvers, aggregate.ApprovalInfoItem{
			UserID:     v.UserId,
			PassedDate: v.PassedDate,
		})
	}
	var approvalInfo = aggregate.ApprovalInfo{
		Auditors:  auditors,
		Approvers: approvers,
	}
	var disposalList []aggregate.DisposalList
	for _, v := range in.DisposalList {
		var permissions []aggregate.FilePermission
		for _, p := range v.Permissions {
			permissions = append(permissions, aggregate.FilePermission{
				FileForm:       int(p.FileForm),
				FilePermission: int(p.FilePermission),
				ReceivedBy:     p.ReceivedBy,
			})
		}
		disposalList = append(disposalList, aggregate.DisposalList{
			InventoryID: v.InventoryId,
			Permissions: permissions,
		})
	}
	request.DisposalList = disposalList
	request.ApprovalInfo = approvalInfo
	return request
}
