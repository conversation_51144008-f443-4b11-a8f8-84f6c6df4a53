package documentlibrarylogic

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/utils"

	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type PreSaveDistributeRecordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewPreSaveDistributeRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PreSaveDistributeRecordLogic {
	return &PreSaveDistributeRecordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 保存发放记录
func (l *PreSaveDistributeRecordLogic) PreSaveDistributeRecord(in *docvault.DocumentDistributeReq) (*docvault.EmptyResp, error) {
	// 构造发放信息
	request := l.reqToRequest(in)

	// 如果id不为空，则表示是更新操作
	if in.Id != "" {
		err := l.svcCtx.Factory.NewDocumentLibrary().UpdateDistributeInventory(l.ctx, request)
		if err != nil {
			l.Logger.Errorf("更新发放清单失败：%v", err)
			return nil, err
		}
		return &docvault.EmptyResp{}, nil
	}

	// 如果id为空，则表示是保存操作
	err := l.svcCtx.Factory.NewDocumentLibrary().SaveDistributeRecord(l.ctx, request)
	if err != nil {
		l.Logger.Errorf("保存发放信息失败：%v", err)
		return nil, err
	}
	return &docvault.EmptyResp{}, nil
}

func (l *PreSaveDistributeRecordLogic) reqToRequest(in *docvault.DocumentDistributeReq) *aggregate.DistributeRecordInfoRequest {
	distributeCount := 0
	request := &aggregate.DistributeRecordInfoRequest{
		ID:                 in.Id,
		Applicant:          utils.GetContextUserID(l.ctx),
		ApplyDate:          in.ApplyDate,
		DistributeType:     in.DistributeType,
		FileType:           in.FileType,
		TypeDictNodeId:     in.TypeDictNodeId,
		OtherReason:        in.OtherReason,
		WishDistributeDate: in.WishDistributeDate,
		FileCategory:       in.FileCategory,
		Reason:             in.Reason,
		Status:             2,
		ApprovalInfo:       aggregate.ApprovalInfo{},
		DistributeList:     nil,
		WorkflowID:         in.WorkflowId,
	}

	for k, distributeInfo := range in.DistributeList {
		request.DistributeList = append(request.DistributeList, aggregate.DistributeList{
			FileID:      distributeInfo.FileId,
			FileName:    distributeInfo.FileName,
			Number:      distributeInfo.Number,
			Version:     distributeInfo.Version,
			Permissions: nil,
		})
		for kk, permission := range distributeInfo.Permissions {
			request.DistributeList[k].Permissions = append(request.DistributeList[k].Permissions, aggregate.Permission{
				FileForm:       permission.FileForm,
				FilePermission: permission.FilePermission,
				Recipient:      permission.Recipient,
				ReceivedBy:     nil,
			})
			distributeCount += len(permission.ReceivedBy)
			for _, received := range permission.ReceivedBy {
				request.DistributeList[k].Permissions[kk].ReceivedBy = append(request.DistributeList[k].Permissions[kk].ReceivedBy, aggregate.ReceivedBy{
					UserId:        received.UserId,
					UserName:      received.UserName,
					SignForStatus: 1,
					DisposeStatus: 1,
				})
			}
		}
	}
	if in.SaveMethod == 1 {
		request.Status = 1
	}
	request.DistributeCount = distributeCount
	return request
}
