package internaldocumentlibrarylogic

import (
	"context"
	"fmt"
	"strconv"

	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/rpc/internal/svc"
	"docvault/rpc/pb/docvault"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLogic {
	return &GetLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取文档
func (l *GetLogic) Get(in *docvault.InternalDocumentGetReq) (*docvault.InternalDocumentGetResp, error) {
	doc, err := mapper.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultDB).GetByID(l.ctx, in.Id)
	if err != nil {
		return nil, err
	}
	return &docvault.InternalDocumentGetResp{
		Id:                doc.ID,
		No:                getViewNo(doc.NoPrefix, doc.SerialNo),
		VersionNo:         getVersionNo(doc.VersionNo),
		OriginalNo:        doc.OriginalNo,
		OriginalVersionNo: doc.OriginalVersionNo,
		Name:              doc.Name,
		DocCategoryId:     doc.DocCategoryID,
		DepartmentId:      doc.DepartmentID,
		AuthorId:          doc.AuthorID,
		PublishDate:       doc.PublishDate.UnixMilli(),
		EffectiveDate:     doc.EffectiveDate.UnixMilli(),
		Status:            int32(doc.Status),
		ApprovalInfo:      buildApprovalInfo(doc.GetApprovalInfo()),
		NoPrefix:          doc.NoPrefix,
		FileId:            doc.FileID,
	}, nil
}

func getViewNo(noPrefix string, no int) string {
	return fmt.Sprintf("%s-%d", noPrefix, no)
}

func buildApprovalInfo(approvalInfo mapper.ApprovalInfo) *docvault.ApprovalInfo {
	var auditors []*docvault.ApprovalInfoItem
	if len(approvalInfo.Approvers) != 0 {
		auditors = make([]*docvault.ApprovalInfoItem, len(approvalInfo.Auditors))
		for i, auditor := range approvalInfo.Auditors {
			auditors[i] = &docvault.ApprovalInfoItem{
				UserId:     auditor.UserID,
				PassedDate: auditor.PassedDate,
			}
		}
	}

	var approvers []*docvault.ApprovalInfoItem
	if len(approvalInfo.Approvers) != 0 {
		approvers = make([]*docvault.ApprovalInfoItem, len(approvalInfo.Approvers))
		for i, approver := range approvalInfo.Approvers {
			approvers[i] = &docvault.ApprovalInfoItem{
				UserId:     approver.UserID,
				PassedDate: approver.PassedDate,
			}
		}
	}

	return &docvault.ApprovalInfo{
		Auditors:  auditors,
		Approvers: approvers,
	}
}

// 序号转版本号，利用阿拉伯数字进行版本计算
// 规则：序号1=A/0, 序号2=A/1, ..., 序号9=A/8, 序号10=B/0, 序号11=B/1, ..., 序号18=B/8, 序号19=C/0
// 每个字母对应9个版本号(0-8)，超过Z后继续循环
func getVersionNo(versionNo int) string {
	if versionNo <= 0 {
		return "A/0" // 默认版本
	}

	// 将序号转换为从0开始的索引
	index := versionNo - 1

	// 计算字母索引 (每9个版本一个字母)
	letterIndex := index / 9

	// 计算数字部分 (0-8)
	numberPart := index % 9

	// 生成字母 (A=0, B=1, C=2, ...)
	letter := string(rune('A' + letterIndex%26)) // 超过Z后循环

	// 返回格式化的版本号
	return letter + "/" + strconv.Itoa(numberPart)
}
