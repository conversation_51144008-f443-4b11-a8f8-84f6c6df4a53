package aggregate

import (
	"context"
	"time"
)

type BorrowRecordService interface {
	// CreateBorrowRecord 创建借阅记录
	CreateBorrowRecord(ctx context.Context, cmd CreateBorrowRecordCmd) error

	// ModifyBorrowRecord 修改借阅记录
	ModifyBorrowRecord(ctx context.Context, cmd ModifyBorrowRecordCmd) error
}

type CreateBorrowRecordCmd struct {
	UserID            string               `json:"userId"`            // 借阅用户ID
	Documents         []BorrowDocumentItem `json:"documents"`         // 借阅的文档列表
	BorrowTime        time.Time            `json:"borrowTime"`        // 借阅时间
	DueTime           time.Time            `json:"dueTime"`           // 应还时间
	BorrowReason      string               `json:"borrowReason"`      // 借阅原因
	BorrowApplyTime   time.Time            `json:"borrowApplyTime"`   // 申请时间
	ApprovalStatus    int32                `json:"approvalStatus"`    // 审批状态
	ApprovalInfo      map[string]any       `json:"approvalInfo"`      // 审批信息
	ApprovalApplyTime time.Time            `json:"approvalApplyTime"` // 审批申请时间
}

type BorrowDocumentItem struct {
	DocumentID    string    `json:"documentId"`    // 文档ID
	VersionNo     string    `json:"versionNo"`     // 文档版本号
	ModuleType    int32     `json:"moduleType"`    // 文档所属模块，1书籍 | 2内部文档 | 3外部文档
	BorrowStatus  int32     `json:"borrowStatus"`  // 文档借阅状态
	RecoverUserID string    `json:"recoverUserId"` // 回收人ID
	RecoverTime   time.Time `json:"recoverTime"`   // 回收时间
}

type ModifyBorrowRecordCmd struct {
	BorrowRecordID    string               `json:"borrowRecordId"`    // 借阅记录ID
	UserID            string               `json:"userId"`            // 借阅用户ID
	Documents         []BorrowDocumentItem `json:"documents"`         // 借阅的文档列表
	BorrowTime        time.Time            `json:"borrowTime"`        // 借阅时间
	DueTime           time.Time            `json:"dueTime"`           // 应还时间
	BorrowReason      string               `json:"borrowReason"`      // 借阅原因
	BorrowApplyTime   time.Time            `json:"borrowApplyTime"`   // 申请时间
	ApprovalStatus    int32                `json:"approvalStatus"`    // 审批状态
	ApprovalInfo      map[string]any       `json:"approvalInfo"`      // 审批信息
	ApprovalApplyTime time.Time            `json:"approvalApplyTime"` // 审批申请时间
}
