package value

// DocumentValidity 文档有效性状态枚举
// 表示文档在借阅时的有效性状态，用于区分不同生命周期的文档
type DocumentValidity int32

const (
	// DocumentValidityAboutToInvalid 即将作废
	// 文档即将失效，但当前仍可借阅
	DocumentValidityAboutToInvalid DocumentValidity = 1

	// DocumentValidityAboutToImplement 即将实施
	// 文档即将生效，但尚未正式实施
	DocumentValidityAboutToImplement DocumentValidity = 2

	// DocumentValidityValid 有效
	// 文档当前有效，正常可借阅状态
	DocumentValidityValid DocumentValidity = 3

	// DocumentValidityToBeRevised 拟修订
	// 文档计划修订，但当前版本仍有效
	DocumentValidityToBeRevised DocumentValidity = 4

	// DocumentValidityInvalid 作废
	// 文档已作废，需要从作废文件库中查找
	DocumentValidityInvalid DocumentValidity = 5
)

// String 返回文档有效性状态的字符串描述
// 功能: 将文档有效性状态枚举值转换为可读的中文描述
// 参数: 无
// 返回值:
//
//	string: 状态的字符串描述
//
// 异常: 无
func (dv DocumentValidity) String() string {
	// 实现步骤:
	// 1. 根据枚举值返回对应的中文描述

	switch dv {
	case DocumentValidityAboutToInvalid:
		return "即将作废"
	case DocumentValidityAboutToImplement:
		return "即将实施"
	case DocumentValidityValid:
		return "有效"
	case DocumentValidityToBeRevised:
		return "拟修订"
	case DocumentValidityInvalid:
		return "作废"
	default:
		return "未知状态"
	}
}

// ValueOf 根据整数值创建文档有效性状态枚举
// 功能: 将整数值转换为对应的文档有效性状态枚举
// 参数:
//
//	v: 整数值
//
// 返回值:
//
//	DocumentValidity: 对应的文档有效性状态枚举
//
// 异常: 无
func (dv DocumentValidity) ValueOf(v int32) DocumentValidity {
	// 实现步骤:
	// 1. 根据整数值返回对应的枚举值
	// 2. 无效值返回默认值

	switch v {
	case 1:
		return DocumentValidityAboutToInvalid
	case 2:
		return DocumentValidityAboutToImplement
	case 3:
		return DocumentValidityValid
	case 4:
		return DocumentValidityToBeRevised
	case 5:
		return DocumentValidityInvalid
	default:
		return DocumentValidityValid
	}
}

// IsValid 验证文档有效性状态是否有效
// 功能: 检查文档有效性状态是否在有效的枚举范围内
// 参数: 无
// 返回值:
//
//	bool: true表示状态有效，false表示无效
//
// 异常: 无
func (dv DocumentValidity) IsValid() bool {
	// 实现步骤:
	// 1. 检查文档有效性状态是否在有效范围内

	return dv >= DocumentValidityAboutToInvalid && dv <= DocumentValidityInvalid
}

// IsFromActiveLibrary 检查是否来自有效文件库
// 功能: 判断当前有效性状态的文档是否应该从有效文件库中查找
// 参数: 无
// 返回值:
//
//	bool: true表示从有效文件库查找，false表示从作废文件库查找
//
// 异常: 无
func (dv DocumentValidity) IsFromActiveLibrary() bool {
	// 实现步骤:
	// 1. 检查是否为作废状态
	// 2. 作废状态从作废文件库查找，其他从有效文件库查找

	return dv != DocumentValidityInvalid
}

// RequiresVersionSelection 检查是否需要选择版本
// 功能: 判断当前有效性状态是否需要用户手动选择文档版本
// 参数: 无
// 返回值:
//
//	bool: true表示需要选择版本，false表示自动生成版本
//
// 异常: 无
func (dv DocumentValidity) RequiresVersionSelection() bool {
	// 实现步骤:
	// 1. 检查是否为作废状态
	// 2. 作废文档需要选择版本，有效文档自动生成

	return dv == DocumentValidityInvalid
}
