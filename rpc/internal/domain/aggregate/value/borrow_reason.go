package value

import "errors"

// BorrowReasonType 借阅原因类型枚举
// 表示用户申请借阅文档的原因类型
type BorrowReasonType int32

const (
	// BorrowReasonTypeProjectReference 项目参考/研究
	// 用于项目开发、研究等工作需要参考相关文档
	BorrowReasonTypeProjectReference BorrowReasonType = 1

	// BorrowReasonTypeProblemInvestigation 问题调查/分析
	// 用于问题排查、故障分析等需要查阅相关文档
	BorrowReasonTypeProblemInvestigation BorrowReasonType = 2

	// BorrowReasonTypeAuditPreparation 审计/检查准备
	// 用于内外部审计、检查工作的准备
	BorrowReasonTypeAuditPreparation BorrowReasonType = 3

	// BorrowReasonTypeTrainingLearning 培训/学习需要
	// 用于员工培训、学习提升等目的
	BorrowReasonTypeTrainingLearning BorrowReasonType = 4

	// BorrowReasonTypeOther 其他
	// 其他未列出的借阅原因，需要额外说明
	BorrowReasonTypeOther BorrowReasonType = 5
)

// BorrowReason 借阅原因值对象
// 封装借阅原因类型和具体说明，确保数据的完整性和一致性
type BorrowReason struct {
	ReasonType       BorrowReasonType // 原因类型
	OtherDescription string           // 其他原因的具体说明
}

// NewBorrowReason 创建借阅原因值对象
// 功能: 创建并验证借阅原因值对象
// 参数:
//
//	reasonType: 原因类型
//	otherDescription: 其他原因说明（当类型为"其他"时必填）
//
// 返回值:
//
//	BorrowReason: 借阅原因值对象
//	error: 错误信息，成功时为nil
//
// 异常: 参数验证失败时返回错误
func NewBorrowReason(reasonType BorrowReasonType, otherDescription string) (BorrowReason, error) {
	// 实现步骤:
	// 1. 验证原因类型是否有效
	// 2. 验证"其他"类型时是否提供了说明
	// 3. 创建值对象

	if !reasonType.IsValid() {
		return BorrowReason{}, errors.New("无效的借阅原因类型")
	}

	if reasonType == BorrowReasonTypeOther && otherDescription == "" {
		return BorrowReason{}, errors.New("选择其他原因时必须提供具体说明")
	}

	if reasonType != BorrowReasonTypeOther && otherDescription != "" {
		return BorrowReason{}, errors.New("非其他原因类型不应提供额外说明")
	}

	return BorrowReason{
		ReasonType:       reasonType,
		OtherDescription: otherDescription,
	}, nil
}

// String 返回借阅原因的完整描述
// 功能: 返回借阅原因的可读描述，包含具体说明
// 参数: 无
// 返回值:
//
//	string: 原因的完整描述
//
// 异常: 无
func (br BorrowReason) String() string {
	if br.ReasonType == BorrowReasonTypeOther {
		return br.OtherDescription
	}
	return br.ReasonType.String()
}

// GetReasonType 获取原因类型
// 功能: 返回借阅原因的类型枚举
// 参数: 无
// 返回值:
//
//	BorrowReasonType: 原因类型枚举
//
// 异常: 无
func (br BorrowReason) GetReasonType() BorrowReasonType {
	return br.ReasonType
}

// GetOtherDescription 获取其他原因说明
// 功能: 返回其他原因的具体说明
// 参数: 无
// 返回值:
//
//	string: 其他原因说明
//
// 异常: 无
func (br BorrowReason) GetOtherDescription() string {
	return br.OtherDescription
}

// IsOtherType 检查是否为其他类型
// 功能: 判断当前借阅原因是否为"其他"类型
// 参数: 无
// 返回值:
//
//	bool: true表示是其他类型，false表示不是
//
// 异常: 无
func (br BorrowReason) IsOtherType() bool {
	return br.ReasonType == BorrowReasonTypeOther
}

// String 返回借阅原因类型的字符串描述
// 功能: 将借阅原因类型枚举值转换为可读的中文描述
// 参数: 无
// 返回值:
//
//	string: 类型的字符串描述
//
// 异常: 无
func (brt BorrowReasonType) String() string {
	// 实现步骤:
	// 1. 根据枚举值返回对应的中文描述

	switch brt {
	case BorrowReasonTypeProjectReference:
		return "项目参考/研究"
	case BorrowReasonTypeProblemInvestigation:
		return "问题调查/分析"
	case BorrowReasonTypeAuditPreparation:
		return "审计/检查准备"
	case BorrowReasonTypeTrainingLearning:
		return "培训/学习需要"
	default: // BorrowReasonTypeOther
		return ""
	}
}

// ValueOf 根据整数值创建借阅原因类型枚举
// 功能: 将整数值转换为对应的借阅原因类型枚举
// 参数:
//
//	v: 整数值
//
// 返回值:
//
//	BorrowReasonType: 对应的借阅原因类型枚举
//
// 异常: 无
func (brt BorrowReasonType) ValueOf(v int32) BorrowReasonType {
	// 实现步骤:
	// 1. 根据整数值返回对应的枚举值
	// 2. 无效值返回默认值

	switch v {
	case 1:
		return BorrowReasonTypeProjectReference
	case 2:
		return BorrowReasonTypeProblemInvestigation
	case 3:
		return BorrowReasonTypeAuditPreparation
	case 4:
		return BorrowReasonTypeTrainingLearning
	case 5:
		return BorrowReasonTypeOther
	default:
		return BorrowReasonTypeProjectReference
	}
}

// IsValid 验证借阅原因类型是否有效
// 功能: 检查借阅原因类型是否在有效的枚举范围内
// 参数: 无
// 返回值:
//
//	bool: true表示类型有效，false表示无效
//
// 异常: 无
func (brt BorrowReasonType) IsValid() bool {
	// 实现步骤:
	// 1. 检查借阅原因类型是否在有效范围内

	return brt >= BorrowReasonTypeProjectReference && brt <= BorrowReasonTypeOther
}

// RequiresOtherDescription 检查是否需要额外的原因描述
// 功能: 判断当前借阅原因类型是否需要用户提供额外的文字说明
// 参数: 无
// 返回值:
//
//	bool: true表示需要额外说明，false表示不需要
//
// 异常: 无
func (brt BorrowReasonType) RequiresOtherDescription() bool {
	// 实现步骤:
	// 1. 检查是否为"其他"类型的原因

	return brt == BorrowReasonTypeOther
}
