package aggregate

import (
	"docvault/rpc/internal/domain/value"
	"errors"
	"time"
)

// BorrowRecord 借阅记录实体
// 表示用户借阅文档的记录信息，支持一次借阅多个文档
type BorrowRecord struct {
	id                string               `json:"id"`                // 主键ID
	userID            string               `json:"userId"`            // 借阅用户ID
	documents         []BorrowDocumentItem `json:"documents"`         // 借阅的文档列表
	borrowStartTime   time.Time            `json:"borrowTime"`        // 借阅开始时间
	borrowEndTime     time.Time            `json:"borrowEndTime"`     // 借阅截止时间
	borrowReason      value.BorrowReason   `json:"borrowReason"`      // 借阅原因（值对象）
	borrowApplyTime   time.Time            `json:"borrowApplyTime"`   // 申请时间
	approvalStatus    value.ApprovalStatus `json:"approvalStatus"`    // 审批状态
	approvalApplyTime time.Time            `json:"approvalApplyTime"` // 审批申请时间
	organizationID    string               `json:"organizationId"`    // 所属组织ID
}

// NewBorrowRecordDraft 创建暂存状态的借阅记录聚合根
// 功能: 创建并初始化借阅记录，设置初始状态为暂存（ApprovalStatus=1）
// 参数:
//
//	id: 记录ID
//	userID: 借阅用户ID
//	documents: 借阅的文档列表
//	borrowStartTime: 借阅开始时间
//	borrowEndTime: 借阅截止时间
//	borrowReason: 借阅原因值对象
//
// 返回值:
//
//	*BorrowRecord: 借阅记录聚合根指针
//
// 异常: 无
func NewBorrowRecordDraft(id, userID string, documents []BorrowDocumentItem, borrowStartTime, borrowEndTime time.Time, borrowReason value.BorrowReason) *BorrowRecord {
	// 实现步骤:
	// 1. 创建借阅记录实例
	// 2. 设置初始状态为暂存（待提交）
	// 3. 设置借阅时间
	// 4. 初始化文档状态为0（未开始借阅）

	// 初始化文档状态
	for i := range documents {
		documents[i].borrowStatus = 0 // 初始状态为0
	}

	return &BorrowRecord{
		id:              id,
		userID:          userID,
		documents:       documents,
		borrowStartTime: borrowStartTime,
		borrowEndTime:   borrowEndTime,
		borrowReason:    borrowReason,
		approvalStatus:  value.ApprovalStatusPending,
	}
}

// NewBorrowRecordSubmitted 创建提交状态的借阅记录聚合根
// 功能: 创建并初始化借阅记录，设置初始状态为提交（ApprovalStatus=2）
// 参数:
//
//	id: 记录ID
//	userID: 借阅用户ID
//	documents: 借阅的文档列表
//	borrowStartTime: 借阅开始时间
//	borrowEndTime: 借阅截止时间
//	borrowReason: 借阅原因值对象
//
// 返回值:
//
//	*BorrowRecord: 借阅记录聚合根指针
//
// 异常: 无
func NewBorrowRecordSubmitted(id, userID string, documents []BorrowDocumentItem, borrowStartTime, borrowEndTime time.Time, borrowReason value.BorrowReason) *BorrowRecord {
	// 实现步骤:
	// 1. 创建借阅记录实例
	// 2. 设置初始状态为提交（待审批）
	// 3. 设置借阅时间
	// 4. 初始化文档状态为0（未开始借阅）

	// 初始化文档状态
	for i := range documents {
		documents[i].borrowStatus = 0 // 初始状态为0
	}

	return &BorrowRecord{
		id:              id,
		userID:          userID,
		documents:       documents,
		borrowStartTime: borrowStartTime,
		borrowEndTime:   borrowEndTime,
		borrowReason:    borrowReason,
		approvalStatus:  value.ApprovalStatusWaiting,
	}
}

// Getter方法 - 提供对私有字段的访问

// ID 获取借阅记录ID
func (br *BorrowRecord) ID() string {
	return br.id
}

// UserID 获取借阅用户ID
func (br *BorrowRecord) UserID() string {
	return br.userID
}

// Documents 获取借阅文档列表
func (br *BorrowRecord) Documents() []BorrowDocumentItem {
	return br.documents
}

// BorrowStartTime 获取借阅开始时间
func (br *BorrowRecord) BorrowStartTime() time.Time {
	return br.borrowStartTime
}

// BorrowEndTime 获取借阅截止时间
func (br *BorrowRecord) BorrowEndTime() time.Time {
	return br.borrowEndTime
}

// BorrowReason 获取借阅原因
func (br *BorrowRecord) BorrowReason() value.BorrowReason {
	return br.borrowReason
}

// BorrowApplyTime 获取申请时间
func (br *BorrowRecord) BorrowApplyTime() time.Time {
	return br.borrowApplyTime
}

// ApprovalStatus 获取审批状态
func (br *BorrowRecord) ApprovalStatus() value.ApprovalStatus {
	return br.approvalStatus
}

// ApprovalApplyTime 获取审批申请时间
func (br *BorrowRecord) ApprovalApplyTime() time.Time {
	return br.approvalApplyTime
}

// OrganizationID 获取所属组织ID
func (br *BorrowRecord) OrganizationID() string {
	return br.organizationID
}

// IsOverdue 判断借阅记录是否过期
// 功能: 通过比较当前时间与借阅截止时间来判断借阅是否逾期
// 参数: 无
// 返回值:
//
//	bool: true表示已过期，false表示未过期
//
// 异常: 无
func (br *BorrowRecord) IsOverdue() bool {
	// 比较当前时间与借阅截止时间
	now := time.Now()
	return now.After(br.borrowEndTime)
}

// SubmitForApproval 提交审批
// 功能: 将暂存状态的借阅记录提交审批，状态从待提交变为待审批
// 参数: 无
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 状态不符合条件时返回错误
func (br *BorrowRecord) SubmitForApproval() error {
	// 实现步骤:
	// 1. 验证当前状态是否为待提交
	// 2. 更新审批状态为待审批

	if br.approvalStatus != value.ApprovalStatusPending {
		return errors.New("只有待提交状态的记录才能提交审批")
	}

	br.approvalStatus = value.ApprovalStatusWaiting
	return nil
}

// Approve 审批通过
// 功能: 将待审批状态的借阅记录审批通过，并将所有文档状态设置为借阅中
// 参数: 无
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 状态不符合条件时返回错误
func (br *BorrowRecord) Approve() error {
	// 实现步骤:
	// 1. 验证当前状态是否为待审批
	// 2. 更新审批状态为已审批
	// 3. 将所有文档状态设置为借阅中

	if br.approvalStatus != value.ApprovalStatusWaiting {
		return errors.New("只有待审批状态的记录才能审批通过")
	}

	br.approvalStatus = value.ApprovalStatusApproved
	// 将所有文档状态设置为借阅中
	for i := range br.documents {
		br.documents[i].borrowStatus = value.BorrowStatusBorrowing
	}
	return nil
}

// Reject 审批驳回
// 功能: 将待审批状态的借阅记录驳回
// 参数: 无
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 状态不符合条件时返回错误
func (br *BorrowRecord) Reject() error {
	// 实现步骤:
	// 1. 验证当前状态是否为待审批
	// 2. 更新审批状态为已驳回

	if br.approvalStatus != value.ApprovalStatusWaiting {
		return errors.New("只有待审批状态的记录才能驳回")
	}

	br.approvalStatus = value.ApprovalStatusRejected
	return nil
}

// StartRecovery 开始回收
// 功能: 将已审批的借阅记录中指定文档状态变更为回收中
// 参数:
//
//	recoverUserID: 回收人ID
//	documentID: 要回收的文档ID
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 状态不符合条件时返回错误
func (br *BorrowRecord) StartRecovery(recoverUserID, documentID string) error {
	// 实现步骤:
	// 1. 验证当前状态是否为已审批
	// 2. 查找指定文档并验证其状态
	// 3. 更新文档借阅状态为回收中
	// 4. 设置回收人ID

	if br.approvalStatus != value.ApprovalStatusApproved {
		return errors.New("只有已审批的记录才能开始回收")
	}

	// 查找指定文档
	for i := range br.documents {
		if br.documents[i].documentID == documentID {
			if br.documents[i].borrowStatus != value.BorrowStatusBorrowing {
				return errors.New("只有借阅中的文档才能开始回收")
			}
			br.documents[i].borrowStatus = value.BorrowStatusRecovering
			br.documents[i].recoverUserID = recoverUserID
			return nil
		}
	}

	return errors.New("未找到指定的文档")
}

// CompleteRecovery 完成回收
// 功能: 将回收中的指定文档状态变更为已回收
// 参数:
//
//	documentID: 要完成回收的文档ID
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 状态不符合条件时返回错误
func (br *BorrowRecord) CompleteRecovery(documentID string) error {
	// 实现步骤:
	// 1. 查找指定文档并验证其状态
	// 2. 更新文档借阅状态为已回收
	// 3. 检查是否所有文档都已回收，如果是则设置回收时间

	// 查找指定文档
	for i := range br.documents {
		if br.documents[i].documentID == documentID {
			if br.documents[i].borrowStatus != value.BorrowStatusRecovering {
				return errors.New("只有回收中的文档才能完成回收")
			}
			br.documents[i].borrowStatus = value.BorrowStatusRecovered
			br.documents[i].recoverTime = time.Now()

			return nil
		}
	}

	return errors.New("未找到指定的文档")
}

// Withdraw 撤销借阅申请
// 功能: 将待审批状态的借阅记录撤销，状态变为待提交
// 参数: 无
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 状态不符合条件时返回错误
func (br *BorrowRecord) Withdraw() error {
	// 实现步骤:
	// 1. 验证当前状态是否为待审批
	// 2. 更新审批状态为待提交

	if br.approvalStatus != value.ApprovalStatusWaiting {
		return errors.New("只有待审批状态的记录才能撤销")
	}

	br.approvalStatus = value.ApprovalStatusPending
	return nil
}

// UpdateDocuments 更新借阅文档列表
// 功能: 更新借阅记录的文档列表，仅在待提交或已驳回状态下可用
// 参数:
//
//	documents: 新的文档列表
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 状态不符合条件时返回错误
func (br *BorrowRecord) UpdateDocuments(documents []BorrowDocumentItem) error {
	// 实现步骤:
	// 1. 验证当前状态是否允许编辑
	// 2. 更新文档列表
	// 3. 初始化文档状态

	if br.approvalStatus != value.ApprovalStatusPending && br.approvalStatus != value.ApprovalStatusRejected {
		return errors.New("只有待提交或已驳回状态的记录才能编辑")
	}

	if len(documents) == 0 {
		return errors.New("借阅文档列表不能为空")
	}

	// 初始化文档状态
	for i := range documents {
		documents[i].borrowStatus = 0 // 初始状态为0
	}

	br.documents = documents
	return nil
}

// UpdateBorrowInfo 更新借阅基本信息
// 功能: 更新借阅记录的基本信息，仅在待提交或已驳回状态下可用
// 参数:
//
//	borrowStartTime: 借阅开始时间
//	borrowEndTime: 借阅截止时间
//	borrowReason: 借阅原因值对象
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 状态不符合条件时返回错误
func (br *BorrowRecord) UpdateBorrowInfo(borrowStartTime, borrowEndTime time.Time, borrowReason value.BorrowReason) error {
	// 实现步骤:
	// 1. 验证当前状态是否允许编辑
	// 2. 验证时间参数
	// 3. 更新借阅信息

	if br.approvalStatus != value.ApprovalStatusPending && br.approvalStatus != value.ApprovalStatusRejected {
		return errors.New("只有待提交或已驳回状态的记录才能编辑")
	}

	if borrowEndTime.Before(time.Now()) {
		return errors.New("借阅截止时间不能早于当前时间")
	}

	if borrowEndTime.Before(borrowStartTime) {
		return errors.New("借阅截止时间不能早于开始时间")
	}

	br.borrowStartTime = borrowStartTime
	br.borrowEndTime = borrowEndTime
	br.borrowReason = borrowReason
	return nil
}

// IsAllDocumentsRecovered 检查是否所有文档都已回收
// 功能: 检查借阅记录中的所有文档是否都已回收
// 参数: 无
// 返回值:
//
//	bool: true表示所有文档都已回收，false表示还有文档未回收
//
// 异常: 无
func (br *BorrowRecord) IsAllDocumentsRecovered() bool {
	// 实现步骤:
	// 1. 遍历所有文档检查状态
	for _, doc := range br.documents {
		if doc.borrowStatus != value.BorrowStatusRecovered {
			return false
		}
	}
	return true
}

// GetRecoveryStatus 获取回收状态统计
// 功能: 统计借阅记录中文档的回收情况
// 参数: 无
// 返回值:
//
//	value.RecoveryStatus: 回收状态枚举
//
// 异常: 无
func (br *BorrowRecord) GetRecoveryStatus() value.RecoveryStatus {
	// 实现步骤:
	// 1. 统计各种状态的文档数量
	// 2. 根据统计结果返回整体回收状态

	if len(br.documents) == 0 {
		return value.RecoveryStatusNone
	}

	recoveredCount := 0
	for _, doc := range br.documents {
		if doc.borrowStatus == value.BorrowStatusRecovered {
			recoveredCount++
		}
	}

	if recoveredCount == 0 {
		return value.RecoveryStatusNone
	} else if recoveredCount == len(br.documents) {
		return value.RecoveryStatusComplete
	} else {
		return value.RecoveryStatusPartial
	}
}

// GetBorrowedDocumentCount 获取借阅文档总数
// 功能: 返回当前借阅记录中的文档总数
// 参数: 无
// 返回值:
//
//	int: 文档总数
//
// 异常: 无
func (br *BorrowRecord) GetBorrowedDocumentCount() int {
	// 实现步骤:
	// 1. 返回文档列表长度

	return len(br.documents)
}

// GetRecoveredDocumentCount 获取已回收文档数量
// 功能: 统计已完成回收的文档数量
// 参数: 无
// 返回值:
//
//	int: 已回收文档数量
//
// 异常: 无
func (br *BorrowRecord) GetRecoveredDocumentCount() int {
	// 实现步骤:
	// 1. 遍历文档列表统计已回收数量

	count := 0
	for _, doc := range br.documents {
		if doc.borrowStatus == value.BorrowStatusRecovered {
			count++
		}
	}
	return count
}

// CanEdit 检查是否可以编辑
// 功能: 判断当前借阅记录是否允许编辑
// 参数: 无
// 返回值:
//
//	bool: true表示可以编辑，false表示不可编辑
//
// 异常: 无
func (br *BorrowRecord) CanEdit() bool {
	// 实现步骤:
	// 1. 检查审批状态是否允许编辑

	return br.approvalStatus == value.ApprovalStatusPending || br.approvalStatus == value.ApprovalStatusRejected
}

// CanDelete 检查是否可以删除
// 功能: 判断当前借阅记录是否允许删除
// 参数: 无
// 返回值:
//
//	bool: true表示可以删除，false表示不可删除
//
// 异常: 无
func (br *BorrowRecord) CanDelete() bool {
	// 实现步骤:
	// 1. 检查审批状态是否允许删除

	return br.approvalStatus == value.ApprovalStatusPending
}

// CanWithdraw 检查是否可以撤销
// 功能: 判断当前借阅记录是否允许撤销
// 参数: 无
// 返回值:
//
//	bool: true表示可以撤销，false表示不可撤销
//
// 异常: 无
func (br *BorrowRecord) CanWithdraw() bool {
	// 实现步骤:
	// 1. 检查审批状态是否允许撤销

	return br.approvalStatus == value.ApprovalStatusWaiting
}

// CanRecover 检查是否可以回收
// 功能: 判断当前借阅记录是否允许进行文档回收
// 参数: 无
// 返回值:
//
//	bool: true表示可以回收，false表示不可回收
//
// 异常: 无
func (br *BorrowRecord) CanRecover() bool {
	// 实现步骤:
	// 1. 检查审批状态是否为已审批

	return br.approvalStatus == value.ApprovalStatusApproved
}

// BorrowDocumentItem 借阅文档项
// 表示单个被借阅的文档信息，包含文档ID、版本号、模块类型、有效性状态和借阅状态
type BorrowDocumentItem struct {
	documentID       string                 `json:"documentId"`       // 文档ID
	documentName     string                 `json:"documentName"`     // 文档名称
	documentNo       string                 `json:"documentNo"`       // 文档编号
	versionNo        string                 `json:"versionNo"`        // 文档版本号
	moduleType       int32                  `json:"moduleType"`       // 文档所属模块，1书籍 | 2内部文档 | 3外部文档
	documentValidity value.DocumentValidity `json:"documentValidity"` // 文档有效性状态
	categoryID       string                 `json:"categoryId"`       // 文件类别ID
	categoryName     string                 `json:"categoryName"`     // 文件类别名称
	borrowStatus     value.BorrowStatus     `json:"borrowStatus"`     // 文档借阅状态
	recoverUserID    string                 `json:"recoverUserId"`    // 回收人ID
	recoverTime      time.Time              `json:"recoverTime"`      // 回收时间
}

// BorrowDocumentItem的Getter方法

// DocumentID 获取文档ID
func (bdi *BorrowDocumentItem) DocumentID() string {
	return bdi.documentID
}

// DocumentName 获取文档名称
func (bdi *BorrowDocumentItem) DocumentName() string {
	return bdi.documentName
}

// DocumentNo 获取文档编号
func (bdi *BorrowDocumentItem) DocumentNo() string {
	return bdi.documentNo
}

// VersionNo 获取文档版本号
func (bdi *BorrowDocumentItem) VersionNo() string {
	return bdi.versionNo
}

// ModuleType 获取文档所属模块
func (bdi *BorrowDocumentItem) ModuleType() int32 {
	return bdi.moduleType
}

// DocumentValidity 获取文档有效性状态
func (bdi *BorrowDocumentItem) DocumentValidity() value.DocumentValidity {
	return bdi.documentValidity
}

// CategoryID 获取文件类别ID
func (bdi *BorrowDocumentItem) CategoryID() string {
	return bdi.categoryID
}

// CategoryName 获取文件类别名称
func (bdi *BorrowDocumentItem) CategoryName() string {
	return bdi.categoryName
}

// BorrowStatus 获取文档借阅状态
func (bdi *BorrowDocumentItem) BorrowStatus() value.BorrowStatus {
	return bdi.borrowStatus
}

// RecoverUserID 获取回收人ID
func (bdi *BorrowDocumentItem) RecoverUserID() string {
	return bdi.recoverUserID
}

// RecoverTime 获取回收时间
func (bdi *BorrowDocumentItem) RecoverTime() time.Time {
	return bdi.recoverTime
}
