package aggregate

import (
	"context"
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/domain/value"
)

//go:generate mockgen -source=document_library_reference.go -destination=mock_document_library_reference.go -package=aggregate
type DocumentLibraryReference interface {
	// 保存发放信息
	CreateDistributeRecord(ctx context.Context, distributeRecordInfo *entity.DistributeRecordInfo) error

	// 根据文件id、文件类型、文件权限类型，查询用户id
	// 文件类型（1内部文件 | 2外部文件），文件权限（1查询 | 2查询/下载 | 3一次下载）
	GetUserIdsByFileIdAndFileFromAndFilePermission(ctx context.Context, fileId string, fileFrom, filePermission int32) (*value.DocPermissionUsers, error)

	// 根据列表id更新发放状态
	AlterDistributeStatus(ctx context.Context, id string, status int32) error

	// 根据流程id更新发放状态
	AlterDistributeStatusByWorkflowId(ctx context.Context, workflowId string, status int32) error

	// 保存回收信息
	SaveRecycleInfo(ctx context.Context, recycleApprovalRequest *entity.RecycleApprovalInfo) error

	// 保存处置信息
	SaveDisposalInfo(ctx context.Context, disposalApprovalRequest *entity.DisposalApprovalInfo) error

	// 获取发放列表
	GetDistributeInfos(ctx context.Context, req *value.GetDistributeListRequest) (int, []*entity.GetDistributeListInfo, error)

	// 保存发放审批流程信息
	SaveDistributeApprovalWorkflow(ctx context.Context, req *value.DistributeApprovalRequest) error

	// 根据发放列表id获取发放清单信息
	GetDistributeInventoryByID(ctx context.Context, id string) ([]*entity.DistributeInventory, error)

	// 根据id更新发放列表信息，先删除旧发放清单数据，再更新发放列表数据，再保存发放清单数据
	UpdateDistributeInventoryByID(ctx context.Context, req *entity.DistributeRecordInfo) error

	// 根据发放列表id删除发放记录
	DeleteDistributeRecordByID(ctx context.Context, id string) error

	// 根据发放列表id和发放清单id更新用户处置状态
	UpdateUserDisposalStatusByID(ctx context.Context, updateUserDisposalStatus *value.UpdateUserDisposalStatus) error
}
