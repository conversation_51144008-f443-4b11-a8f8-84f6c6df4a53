package aggregate

import (
	"context"
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/domain/value"
	"docvault/utils"

	"github.com/zeromicro/go-zero/core/logc"
)

type DocumentLibrary struct {
	DocumentLibraryReference
	BasicAbilityReference
}

func NewDocumentLibrary(documentLibraryReference DocumentLibraryReference, basicAbilityReference BasicAbilityReference) *DocumentLibrary {
	return &DocumentLibrary{
		DocumentLibraryReference: documentLibraryReference,
		BasicAbilityReference:    basicAbilityReference,
	}
}

func (d *DocumentLibrary) SaveDistributeRecord(ctx context.Context, req *DistributeRecordInfoRequest) error {
	distributeRecordInfo := &entity.DistributeRecordInfo{}
	req.ID = d.GenerateID()
	err := utils.StructCopy(ctx, distributeRecordInfo, req)
	if err != nil {
		logc.Error(ctx, "发放信息 copy 失败:", err)
		return err
	}
	err = d.CreateDistributeRecord(ctx, distributeRecordInfo)
	if err != nil {
		logc.Error(ctx, "发放信息创建失败：", err)
		return err
	}
	return nil
}

func (d *DocumentLibrary) GetDocPermissionUsers(ctx context.Context, req *GetDocPermissionUsersRequest) (*DocPermissionUsers, error) {
	docPermissionUsers, err := d.GetUserIdsByFileIdAndFileFromAndFilePermission(ctx, req.FileId, req.FileFrom, req.FilePermission)
	if err != nil {
		logc.Error(ctx, "获取文件权限用户失败：", err)
		return nil, err
	}
	return &DocPermissionUsers{
		WaitForApprovalUsers: docPermissionUsers.WaitForApprovalUsers,
		NotRecycledUsers:     docPermissionUsers.NotRecycledUsers,
		RecycleFlowUsers:     docPermissionUsers.RecycleFlowUsers,
	}, nil
}

func (d *DocumentLibrary) UpdateDistributeStatus(ctx context.Context, id string, status int32) error {
	err := d.AlterDistributeStatus(ctx, id, status)
	return err
}

func (d *DocumentLibrary) UpdateDistributeStatusByWorkflowId(ctx context.Context, workflowId string, status int32) error {
	err := d.AlterDistributeStatusByWorkflowId(ctx, workflowId, status)
	return err
}

func (d *DocumentLibrary) SaveRecycleApprovalInfo(ctx context.Context, req *RecycleApprovalRequest) error {
	recycleApprovalInfo := &entity.RecycleApprovalInfo{}
	err := utils.StructCopy(ctx, recycleApprovalInfo, req)
	if err != nil {
		logc.Error(ctx, "回收审批信息 copy 失败:", err)
		return err
	}
	err = d.SaveRecycleInfo(ctx, recycleApprovalInfo)
	if err != nil {
		logc.Error(ctx, "回收审批信息保存失败：", err)
		return err
	}
	return nil
}

func (d *DocumentLibrary) SaveDisposalApprovalInfo(ctx context.Context, req *DisposalApprovalRequest) error {
	disposalApprovalInfo := &entity.DisposalApprovalInfo{}
	err := utils.StructCopy(ctx, disposalApprovalInfo, req)
	if err != nil {
		logc.Error(ctx, "处置审批信息 copy 失败:", err)
		return err
	}
	for _, v := range req.DisposalList {
		for _, permission := range v.Permissions {
			disposalApprovalInfo.DisposalCount += len(permission.ReceivedBy)
		}
	}
	err = d.SaveDisposalInfo(ctx, disposalApprovalInfo)
	if err != nil {
		logc.Error(ctx, "处置审批信息保存失败：", err)
		return err
	}
	return nil
}

func (d *DocumentLibrary) GetDistributeListInfos(ctx context.Context, req *GetDistributeListRequest) (*GetDistributeListResp, error) {
	getDistributeListRequest := value.GetDistributeListRequest{
		Page:           req.PageInfo.Page,
		PageSize:       req.PageInfo.PageSize,
		NoPage:         req.PageInfo.NoPage,
		FileNumber:     req.FileNumber,
		FileName:       req.FileName,
		FileType:       req.FileType,
		FileCategory:   req.FileCategory,
		DistributeType: req.DistributeType,
		Status:         req.Status,
		Applicant:      req.Applicant,
	}
	total, distributeListInfos, err := d.GetDistributeInfos(ctx, &getDistributeListRequest)
	if err != nil {
		logc.Error(ctx, "获取发放列表信息失败：", err)
		return nil, err
	}
	// 数据转换
	getDistributeListInfos := d.distributeDataConversion(ctx, distributeListInfos)
	return &GetDistributeListResp{
		Total: int64(total),
		Data:  getDistributeListInfos,
	}, nil
}
func (d *DocumentLibrary) distributeDataConversion(ctx context.Context, distributeListInfos []*entity.GetDistributeListInfo) []GetDistributeListInfo {
	var getDistributeListInfos []GetDistributeListInfo
	for _, v := range distributeListInfos {
		var received []DistributeUser
		for _, r := range v.Received {
			received = append(received, DistributeUser{
				UserID:   r.UserID,
				Nickname: d.TranslateUserNickname(ctx, r.UserID),
				FileForm: r.FileForm,
			})
		}
		var notReceived []DistributeUser
		for _, r := range v.NotReceived {
			notReceived = append(notReceived, DistributeUser{
				UserID:   r.UserID,
				Nickname: d.TranslateUserNickname(ctx, r.UserID),
				FileForm: r.FileForm,
			})
		}
		var recycle []DistributeUser
		for _, r := range v.Recycle {
			recycle = append(recycle, DistributeUser{
				UserID:   r.UserID,
				Nickname: d.TranslateUserNickname(ctx, r.UserID),
				FileForm: r.FileForm,
			})
		}
		var approvalInfo ApprovalInfo
		for _, r := range v.ApprovalInfo.Approvers {
			approvalInfo.Approvers = append(approvalInfo.Approvers, ApprovalInfoItem{
				UserID:     r.UserID,
				PassedDate: r.PassedDate,
				Nickname:   d.TranslateUserNickname(ctx, r.UserID),
			})
		}
		for _, r := range v.ApprovalInfo.Auditors {
			approvalInfo.Auditors = append(approvalInfo.Auditors, ApprovalInfoItem{
				UserID:     r.UserID,
				PassedDate: r.PassedDate,
				Nickname:   d.TranslateUserNickname(ctx, r.UserID),
			})
		}
		getDistributeListInfos = append(getDistributeListInfos, GetDistributeListInfo{
			ID:                 v.ID,
			ApplyDate:          v.ApplyDate,
			WishDistributeDate: v.WishDistributeDate,
			Reason:             v.Reason,
			OtherReason:        v.OtherReason,
			WorkflowID:         v.WorkflowID,
			ApprovalInfo:       approvalInfo,
			FileType:           v.FileType,
			FileCategory:       v.FileCategory,
			DistributeType:     v.DistributeType,
			Status:             v.Status,
			Applicant:          d.TranslateUserNickname(ctx, v.Applicant),
			Received:           received,
			NotReceived:        notReceived,
			Recycle:            recycle,
		})
	}
	return getDistributeListInfos
}

func (d *DocumentLibrary) SaveDistributeApproval(ctx context.Context, req *DistributeApprovalRequest) error {
	// 发放审批流程信息
	distributeApprovalInfo := &value.DistributeApprovalRequest{}
	err := utils.StructCopy(ctx, distributeApprovalInfo, req)
	if err != nil {
		logc.Error(ctx, "发放审批信息 copy 失败:", err)
		return err
	}
	// 持久化发放审批流程信息
	err = d.SaveDistributeApprovalWorkflow(ctx, distributeApprovalInfo)
	if err != nil {
		logc.Error(ctx, "发放审批信息保存失败：", err)
		return err
	}
	return nil
}

func (d *DocumentLibrary) GetDistributeInventories(ctx context.Context, id string) ([]DistributeInventory, error) {
	distributeInventories, err := d.GetDistributeInventoryByID(ctx, id)
	if err != nil {
		logc.Error(ctx, "获取发放清单信息失败：", err)
		return nil, err
	}
	var distributeInventorys []DistributeInventory
	for _, v := range distributeInventories {
		var permissions []PermissionResp
		for _, p := range v.Permissions {
			var receivedBy []Received
			for _, r := range p.ReceivedBy {
				receivedBy = append(receivedBy, Received{
					UserID:   r.UserID,
					Status:   r.Status,
					Nickname: r.Nickname,
				})
			}
			permissions = append(permissions, PermissionResp{
				FileForm:       p.FileForm,
				FilePermission: p.FilePermission,
				ReceivedBy:     receivedBy,
			})
		}
		distributeInventorys = append(distributeInventorys, DistributeInventory{
			ID:          v.ID,
			FileId:      v.FileId,
			FileName:    v.FileName,
			Number:      v.Number,
			Version:     v.Version,
			Permissions: permissions,
		})
	}
	return distributeInventorys, nil
}

func (d *DocumentLibrary) UpdateDistributeInventory(ctx context.Context, req *DistributeRecordInfoRequest) error {
	distributeRecordInfo := &entity.DistributeRecordInfo{}
	err := utils.StructCopy(ctx, distributeRecordInfo, req)
	if err != nil {
		logc.Error(ctx, "发放信息 copy 失败:", err)
		return err
	}

	// 更新发放列表信息，保存发放清单信息
	err = d.UpdateDistributeInventoryByID(ctx, distributeRecordInfo)
	if err != nil {
		logc.Error(ctx, "更新发放清单信息失败：", err)
		return err
	}

	return nil
}

func (d *DocumentLibrary) DeleteDistributeRecord(ctx context.Context, id string) error {
	err := d.DeleteDistributeRecordByID(ctx, id)
	return err
}

func (d *DocumentLibrary) UpdateUserDisposalStatus(ctx context.Context, updateUserDisposalStatusRequest UpdateUserDisposalStatusRequest) error {
	// 封装更新用户处置状态的实体
	var updateUserDisposalStatus *value.UpdateUserDisposalStatus
	var recycleList []value.RecycleList
	for _, v := range updateUserDisposalStatusRequest.Recycles {
		var permissions []value.FilePermission
		for _, p := range v.Permissions {
			permissions = append(permissions, value.FilePermission{
				FileForm:       p.FileForm,
				FilePermission: p.FilePermission,
				ReceivedBy:     p.ReceivedBy,
			})
		}
		recycleList = append(recycleList, value.RecycleList{
			InventoryID: v.InventoryID,
			Permissions: permissions,
		})
	}
	updateUserDisposalStatus.DistributeID = updateUserDisposalStatusRequest.DistributeID
	updateUserDisposalStatus.DisposalStatus = updateUserDisposalStatusRequest.DisposalStatus
	updateUserDisposalStatus.Recycles = recycleList
	// 更新用户处置状态
	err := d.UpdateUserDisposalStatusByID(ctx, updateUserDisposalStatus)
	return err
}
