package aggregate

import (
	"context"
	"docvault/utils"
	"errors"
	"testing"

	"docvault/rpc/internal/infrastructure/adapter/mapper"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestDocumentLibrary_SaveDistributeRecord(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDocRef := NewMockDocumentLibraryReference(ctrl)
	mockBasicRef := NewMockBasicAbilityReference(ctrl)

	service := NewDocumentLibrary(mockDocRef, mockBasicRef)
	ctx := context.Background()

	request := &DistributeRecordInfoRequest{
		Applicant:          "user1",
		ApplyDate:          1234567890,
		DistributeType:     1,
		FileType:           1,
		FileCategory:       "cat1",
		TypeDictNodeId:     "type1",
		Reason:             "reason1",
		OtherReason:        "other",
		WishDistributeDate: 1234567999,
		DistributeCount:    2,
		SignForCount:       2,
		DisposeCount:       0,
		Status:             1,
		ApprovalInfo: ApprovalInfo{
			Auditors:  []ApprovalInfoItem{{UserID: "a1", PassedDate: 123}},
			Approvers: []ApprovalInfoItem{{UserID: "b1", PassedDate: 456}},
		},
		DistributeList: []DistributeList{{FileName: "f1", Number: "n1", Version: "v1"}},
	}

	Convey("Test PreSaveDistributeRecord", t, func() {
		Convey("Success", func() {
			mockDocRef.EXPECT().CreateDistributeRecord(gomock.Any(), gomock.Any()).Return(nil)
			err := service.SaveDistributeRecord(ctx, request)
			So(err, ShouldBeNil)
		})

		Convey("StructCopy fail", func() {
			patches := gomonkey.NewPatches()
			defer patches.Reset()
			patches.ApplyFunc(utils.StructCopy, func(ctx context.Context, to, from any) error {
				return errors.New("copy error")
			})
			err := service.SaveDistributeRecord(ctx, request)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "copy error")
		})

		Convey("CreateDistributeRecord fail", func() {
			patches := gomonkey.NewPatches()
			defer patches.Reset()
			// StructCopy成功，CreateDistributeRecord失败
			patches.ApplyFunc(utils.StructCopy, func(ctx context.Context, to, from any) error {
				return nil
			})
			mockDocRef.EXPECT().CreateDistributeRecord(gomock.Any(), gomock.Any()).Return(errors.New("db error"))
			err := service.SaveDistributeRecord(ctx, request)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "db error")
		})
	})
}

func TestDocumentLibrary_UpdateDistributeInventory(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDocRef := NewMockDocumentLibraryReference(ctrl)
	mockBasicRef := NewMockBasicAbilityReference(ctrl)

	service := NewDocumentLibrary(mockDocRef, mockBasicRef)
	ctx := context.Background()

	request := &DistributeRecordInfoRequest{
		ID:                 "dist1",
		Applicant:          "user1",
		ApplyDate:          1234567890,
		DistributeType:     1,
		FileType:           1,
		FileCategory:       "cat1",
		TypeDictNodeId:     "type1",
		Reason:             "reason1",
		OtherReason:        "other",
		WishDistributeDate: 1234567999,
		DistributeCount:    2,
		SignForCount:       2,
		DisposeCount:       0,
		Status:             1,
		ApprovalInfo: ApprovalInfo{
			Auditors:  []ApprovalInfoItem{{UserID: "a1", PassedDate: 123}},
			Approvers: []ApprovalInfoItem{{UserID: "b1", PassedDate: 456}},
		},
		DistributeList: []DistributeList{{FileName: "f1", Number: "n1", Version: "v1"}},
	}

	Convey("Test UpdateDistributeInventory", t, func() {
		Convey("Success", func() {
			// 模拟事务
			tx := &mockTransaction{}
			mockBasicRef.EXPECT().StartTX(ctx).Return(tx)

			// 模拟更新操作成功
			mockDocRef.EXPECT().UpdateDistributeInventoryByID(ctx, gomock.Any()).Return(nil)

			err := service.UpdateDistributeInventory(ctx, request)
			So(err, ShouldBeNil)
		})

		Convey("StartTX fail", func() {
			mockBasicRef.EXPECT().StartTX(ctx).Return(nil)

			err := service.UpdateDistributeInventory(ctx, request)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "启动事务失败")
		})

		Convey("UpdateDistributeInventoryByID fail", func() {
			// 模拟事务
			tx := &mockTransaction{}
			mockBasicRef.EXPECT().StartTX(ctx).Return(tx)

			// 模拟更新操作失败
			mockDocRef.EXPECT().UpdateDistributeInventoryByID(ctx, gomock.Any()).Return(errors.New("update error"))

			err := service.UpdateDistributeInventory(ctx, request)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "update error")
		})

		Convey("StructCopy fail", func() {
			// 模拟事务
			tx := &mockTransaction{}
			mockBasicRef.EXPECT().StartTX(ctx).Return(tx)

			// 模拟 StructCopy 失败
			patches := gomonkey.NewPatches()
			defer patches.Reset()
			patches.ApplyFunc(utils.StructCopy, func(ctx context.Context, to, from any) error {
				return errors.New("copy error")
			})

			err := service.UpdateDistributeInventory(ctx, request)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "copy error")
		})

		Convey("UpdateDistributeInventoryByID fail 2", func() {
			// 模拟事务
			tx := &mockTransaction{}
			mockBasicRef.EXPECT().StartTX(ctx).Return(tx)

			// 模拟更新操作失败
			mockDocRef.EXPECT().UpdateDistributeInventoryByID(ctx, gomock.Any()).Return(errors.New("update error"))

			err := service.UpdateDistributeInventory(ctx, request)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "update error")
		})
	})
}

// mockTransaction 模拟事务接口
type mockTransaction struct{}

func (m *mockTransaction) Begin(ctx context.Context) (mapper.Transaction, error) {
	return m, nil
}

func (m *mockTransaction) GetTX() *gorm.DB {
	return nil
}

func (m *mockTransaction) Commit() error {
	return nil
}

func (m *mockTransaction) Rollback() error {
	return nil
}

func (m *mockTransaction) AutoCommit(err *error) {
	// 模拟自动提交逻辑
	if *err != nil {
		m.Rollback()
	} else {
		m.Commit()
	}
}
