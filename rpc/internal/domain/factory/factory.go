//go:generate mockgen -source=factory.go -destination=mock_factory.go -package=factory
package factory

import (
	"docvault/rpc/internal/domain/aggregate"
)

type Factory interface {
	NewInternalDocumentLibrary() aggregate.InternalDocumentLibraryService
	NewExternalDocumentLibrary() aggregate.ExternalDocumentLibraryService
	NewDocumentLibrary() aggregate.DocumentLibraryService
	NewBorrowRecordService() aggregate.BorrowRecordService
}
