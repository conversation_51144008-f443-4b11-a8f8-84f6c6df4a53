package factory

import (
	"docvault/rpc/internal/config"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/rpc/internal/infrastructure/serviceimpl"
)

type Impl struct {
	c                                config.Config
	basicAbilityReference            aggregate.BasicAbilityReference
	internalDocumentLibraryReference aggregate.InternalDocumentLibraryReference
	externalDocumentLibraryReference aggregate.ExternalDocumentLibraryReference
	documentLibraryReference         aggregate.DocumentLibraryReference
	docvaultDB                       *mapper.DocvaultDB
}

func NewImpl(
	c config.Config,
	basicAbilityReference aggregate.BasicAbilityReference,
	internalDocumentLibraryReference aggregate.InternalDocumentLibraryReference,
	externalDocumentLibraryReference aggregate.ExternalDocumentLibraryReference,
	documentLibraryReference aggregate.DocumentLibraryReference,
	docvaultDB *mapper.DocvaultDB,
) Factory {
	return &Impl{
		c: c, basicAbilityReference: basicAbilityReference,
		internalDocumentLibraryReference: internalDocumentLibraryReference,
		externalDocumentLibraryReference: externalDocumentLibraryReference,
		documentLibraryReference:         documentLibraryReference,
		docvaultDB:                       docvaultDB,
	}
}

func (i *Impl) NewInternalDocumentLibrary() aggregate.InternalDocumentLibraryService {
	return aggregate.NewInternalDocumentLibrary(i.internalDocumentLibraryReference, i.basicAbilityReference)
}

func (i *Impl) NewExternalDocumentLibrary() aggregate.ExternalDocumentLibraryService {
	return aggregate.NewExternalDocumentLibrary(i.externalDocumentLibraryReference, i.basicAbilityReference)
}

func (i *Impl) NewDocumentLibrary() aggregate.DocumentLibraryService {
	return aggregate.NewDocumentLibrary(i.documentLibraryReference, i.basicAbilityReference)
}

func (i *Impl) NewBorrowRecordService() aggregate.BorrowRecordService {
	return serviceimpl.NewBorrowRecordServiceImpl(i.docvaultDB, i.basicAbilityReference)
}
