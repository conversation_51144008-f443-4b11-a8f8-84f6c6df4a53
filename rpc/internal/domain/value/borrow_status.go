package value

// BorrowStatus 借阅状态枚举
// 表示借阅记录的借阅状态，用于跟踪借阅流程的各个阶段
type BorrowStatus int32

const (
	// BorrowStatusBorrowing 借阅中
	// 文档正在被借阅使画
	BorrowStatusBorrowing BorrowStatus = 1

	// BorrowStatusRecovering 回收中
	// 文档正在回收过程中
	BorrowStatusRecovering BorrowStatus = 2

	// BorrowStatusRecovered 已回收
	// 借阅的文档已被回收
	BorrowStatusRecovered BorrowStatus = 3
)

func (bs BorrowStatus) ValueOf(v int32) BorrowStatus {
	switch v {
	case 1:
		return BorrowStatusBorrowing
	case 2:
		return BorrowStatusRecovering
	case 3:
		return BorrowStatusRecovered
	default:
		return BorrowStatusBorrowing
	}
}

// String 返回借阅状态的字符串描述
// 功能: 将借阅状态枚举值转换为可读的字符串描述
// 参数: 无
// 返回值:
//
//	string: 状态的字符串描述
//
// 异常: 无
func (bs BorrowStatus) String() string {
	// 实现步骤:
	// 1. 根据枚举值返回对应的中文描述

	switch bs {
	case BorrowStatusRecovered:
		return "已回收"
	case BorrowStatusBorrowing:
		return "借阅中"
	case BorrowStatusRecovering:
		return "回收中"
	default:
		return "未知状态"
	}
}

// IsValid 验证借阅状态是否有效
// 功能: 检查借阅状态是否在有效的枚举范围内
// 参数: 无
// 返回值:
//
//	bool: true表示状态有效，false表示无效
//
// 异常: 无
func (bs BorrowStatus) IsValid() bool {
	// 实现步骤:
	// 1. 检查借阅状态是否在有效范围内

	return bs >= BorrowStatusBorrowing && bs <= BorrowStatusRecovered
}
