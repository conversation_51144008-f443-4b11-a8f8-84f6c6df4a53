package value

// RecoveryStatus 回收状态统计
// 表示借阅记录中文档的整体回收情况
type RecoveryStatus int32

const (
	// RecoveryStatusNone 均未回收
	// 所有文档都未开始回收流程
	RecoveryStatusNone RecoveryStatus = 1

	// RecoveryStatusPartial 部分回收
	// 部分文档已回收，部分文档仍在借阅或回收中
	RecoveryStatusPartial RecoveryStatus = 2

	// RecoveryStatusComplete 全部回收
	// 所有文档都已完成回收
	RecoveryStatusComplete RecoveryStatus = 3
)

// String 返回回收状态的字符串描述
// 功能: 将回收状态枚举值转换为可读的中文描述
// 参数: 无
// 返回值:
//
//	string: 状态的字符串描述
//
// 异常: 无
func (rs RecoveryStatus) String() string {
	// 实现步骤:
	// 1. 根据枚举值返回对应的中文描述

	switch rs {
	case RecoveryStatusNone:
		return "均未回收"
	case RecoveryStatusPartial:
		return "部分回收"
	case RecoveryStatusComplete:
		return "全部回收"
	default:
		return "未知状态"
	}
}

// GetDisplayColor 获取状态显示颜色
// 功能: 根据回收状态返回对应的显示颜色标识
// 参数: 无
// 返回值:
//
//	string: 颜色标识（red/yellow/green）
//
// 异常: 无
func (rs RecoveryStatus) GetDisplayColor() string {
	// 实现步骤:
	// 1. 根据回收状态返回对应的颜色
	// 2. 全部回收：绿色，部分回收：黄色，均未回收：红色

	switch rs {
	case RecoveryStatusNone:
		return "red"
	case RecoveryStatusPartial:
		return "yellow"
	case RecoveryStatusComplete:
		return "green"
	default:
		return "gray"
	}
}

// ValueOf 根据整数值创建回收状态枚举
// 功能: 将整数值转换为对应的回收状态枚举
// 参数:
//
//	v: 整数值
//
// 返回值:
//
//	RecoveryStatus: 对应的回收状态枚举
//
// 异常: 无
func (rs RecoveryStatus) ValueOf(v int32) RecoveryStatus {
	// 实现步骤:
	// 1. 根据整数值返回对应的枚举值
	// 2. 无效值返回默认值

	switch v {
	case 1:
		return RecoveryStatusNone
	case 2:
		return RecoveryStatusPartial
	case 3:
		return RecoveryStatusComplete
	default:
		return RecoveryStatusNone
	}
}

// IsValid 验证回收状态是否有效
// 功能: 检查回收状态是否在有效的枚举范围内
// 参数: 无
// 返回值:
//
//	bool: true表示状态有效，false表示无效
//
// 异常: 无
func (rs RecoveryStatus) IsValid() bool {
	// 实现步骤:
	// 1. 检查回收状态是否在有效范围内

	return rs >= RecoveryStatusNone && rs <= RecoveryStatusComplete
}
