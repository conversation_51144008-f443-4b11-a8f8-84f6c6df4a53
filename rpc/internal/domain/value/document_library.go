package value

type GetDocPermissionUser struct {
	DistributeDocPermissionUsers []DistributeDocPermissionUser `json:"distributeDocPermissionUsers"`
	RecycleDocPermissionUsers    []RecycleDocPermissionUser    `json:"recycleDocPermissionUsers"`
}

type DistributeDocPermissionUser struct {
	DistributeStatus int    `json:"distributeStatus"` // 发放状态，2待审批 | 3已审批 | 4已回收
	UserID           string `json:"user_id"`          // 用户ID
}

type RecycleDocPermissionUser struct {
	RecycleStatus int    `json:"recycleStatus"` // 回收状态，2待审批
	UserID        string `json:"user_id"`       // 用户ID
}

type GetDistributeListRequest struct {
	Page           uint64   `json:"page"`
	PageSize       uint64   `json:"pageSize"`
	NoPage         bool     `json:"noPage"`
	FileNumber     string   `form:"fileNumber,optional"`     // 文件编号
	FileName       string   `form:"fileName,optional"`       // 文件名称
	FileType       int32    `form:"fileType,optional"`       // 文件类型
	FileCategory   []string `form:"fileCategory,optional"`   // 文件类别id
	DistributeType int32    `form:"distributeType,optional"` // 发放类型
	Status         int32    `form:"status,optional"`         // 状态，0全部 | 1待提交 | 2待审批 | 3已审批 | 4已驳回
	Applicant      []string `form:"applicant,optional"`      // 申请人id
}

type DistributeApprovalRequest struct {
	WorkflowID   string       `json:"workflowId"`   // 流程id
	Status       int          `json:"status"`       // 审批状态
	ApprovalInfo ApprovalInfo `json:"approvalInfo"` // 审批信息
}

type DocPermissionUsers struct {
	WaitForApprovalUsers []string // 待审批用户
	NotRecycledUsers     []string // 未回收用户
	RecycleFlowUsers     []string // 回收流程中用户
}

// DetailedUserPermission 详细的用户权限信息
type DetailedUserPermission struct {
	UserID           string `json:"userId"`           // 用户ID
	DistributeStatus int    `json:"distributeStatus"` // 发放状态，2待审批 | 3已审批 | 4已回收
	SignForStatus    int32  `json:"signForStatus"`    // 签收状态，1未签收 | 2已签收
	DisposeStatus    int32  `json:"disposeStatus"`    // 处置状态，1未回收 | 2回收审批中 | 3已回收
}

type UpdateUserDisposalStatus struct {
	DistributeID   string
	DisposalStatus int32
	Recycles       []RecycleList
}

type RecycleList struct {
	InventoryID string           `json:"inventoryId"` // 清单列表ID
	Permissions []FilePermission `json:"permissions"` // 文件权限
}

type FilePermission struct {
	FileForm       int      `json:"fileForm"`       // 文件形式
	FilePermission int      `json:"filePermission"` // 文件权限
	ReceivedBy     []string `json:"receivedBy"`     // 接收人
}
