package value

// BorrowReason 借阅原因枚举
// 表示用户申请借阅文档的原因类型
type BorrowReason int32

const (
	// BorrowReasonProjectReference 项目参考/研究
	// 用于项目开发、研究等工作需要参考相关文档
	BorrowReasonProjectReference BorrowReason = 1

	// BorrowReasonProblemInvestigation 问题调查/分析
	// 用于问题排查、故障分析等需要查阅相关文档
	BorrowReasonProblemInvestigation BorrowReason = 2

	// BorrowReasonAuditPreparation 审计/检查准备
	// 用于内外部审计、检查工作的准备
	BorrowReasonAuditPreparation BorrowReason = 3

	// BorrowReasonTrainingLearning 培训/学习需要
	// 用于员工培训、学习提升等目的
	BorrowReasonTrainingLearning BorrowReason = 4

	// BorrowReasonOther 其他
	// 其他未列出的借阅原因，需要额外说明
	BorrowReasonOther BorrowReason = 5
)

// String 返回借阅原因的字符串描述
// 功能: 将借阅原因枚举值转换为可读的中文描述
// 参数: 无
// 返回值:
//
//	string: 原因的字符串描述
//
// 异常: 无
func (br BorrowReason) String() string {
	// 实现步骤:
	// 1. 根据枚举值返回对应的中文描述

	switch br {
	case BorrowReasonProjectReference:
		return "项目参考/研究"
	case BorrowReasonProblemInvestigation:
		return "问题调查/分析"
	case BorrowReasonAuditPreparation:
		return "审计/检查准备"
	case BorrowReasonTrainingLearning:
		return "培训/学习需要"
	case BorrowReasonOther:
		return "其他"
	default:
		return "未知原因"
	}
}

// ValueOf 根据整数值创建借阅原因枚举
// 功能: 将整数值转换为对应的借阅原因枚举
// 参数:
//
//	v: 整数值
//
// 返回值:
//
//	BorrowReason: 对应的借阅原因枚举
//
// 异常: 无
func (br BorrowReason) ValueOf(v int32) BorrowReason {
	// 实现步骤:
	// 1. 根据整数值返回对应的枚举值
	// 2. 无效值返回默认值

	switch v {
	case 1:
		return BorrowReasonProjectReference
	case 2:
		return BorrowReasonProblemInvestigation
	case 3:
		return BorrowReasonAuditPreparation
	case 4:
		return BorrowReasonTrainingLearning
	case 5:
		return BorrowReasonOther
	default:
		return BorrowReasonProjectReference
	}
}

// IsValid 验证借阅原因是否有效
// 功能: 检查借阅原因是否在有效的枚举范围内
// 参数: 无
// 返回值:
//
//	bool: true表示原因有效，false表示无效
//
// 异常: 无
func (br BorrowReason) IsValid() bool {
	// 实现步骤:
	// 1. 检查借阅原因是否在有效范围内

	return br >= BorrowReasonProjectReference && br <= BorrowReasonOther
}

// RequiresOtherDescription 检查是否需要额外的原因描述
// 功能: 判断当前借阅原因是否需要用户提供额外的文字说明
// 参数: 无
// 返回值:
//
//	bool: true表示需要额外说明，false表示不需要
//
// 异常: 无
func (br BorrowReason) RequiresOtherDescription() bool {
	// 实现步骤:
	// 1. 检查是否为"其他"类型的原因

	return br == BorrowReasonOther
}
