package value

// ApprovalStatus 审批状态枚举
// 表示审批流程的状态，用于跟踪审批的进度
type ApprovalStatus int32

const (
	// ApprovalStatusPending 待提交
	// 新增后暂存的状态，该状态仅申请人自己可见，此状态下，申请人可删除数据
	ApprovalStatusPending ApprovalStatus = 1
	// ApprovalStatusWaiting 待审批
	// 审批中的状态，文件管理员可见所有，申请人仅能见自己的数据
	ApprovalStatusWaiting ApprovalStatus = 2
	// ApprovalStatusRejected 已驳回
	// 审批被驳回后的状态，该状态文件管理员可见全部，申请人仅可见自己的数据
	ApprovalStatusRejected ApprovalStatus = 3
	// ApprovalStatusApproved 已审批
	// 审批通过的状态，该状态文件管理员可见全部，申请人仅可见自己的数据
	ApprovalStatusApproved ApprovalStatus = 4
)

// String 返回审批状态的字符串描述
// 功能: 将审批状态枚举值转换为可读的字符串描述
// 参数: 无
// 返回值:
//
//	string: 状态的字符串描述
//
// 异常: 无
func (as ApprovalStatus) String() string {
	// 实现步骤:
	// 1. 根据枚举值返回对应的中文描述

	switch as {
	case ApprovalStatusPending:
		return "待提交"
	case ApprovalStatusWaiting:
		return "待审批"
	case ApprovalStatusRejected:
		return "已驳回"
	case ApprovalStatusApproved:
		return "已审批"
	default:
		return "未知状态"
	}
}

func (as ApprovalStatus) ValueOf(v int32) ApprovalStatus {
	switch v {
	case 1:
		return ApprovalStatusPending
	case 2:
		return ApprovalStatusWaiting
	case 3:
		return ApprovalStatusRejected
	case 4:
		return ApprovalStatusApproved
	default:
		return ApprovalStatusPending
	}
}

// IsValid 验证审批状态是否有效
// 功能: 检查审批状态是否在有效的枚举范围内
// 参数: 无
// 返回值:
//
//	bool: true表示状态有效，false表示无效
//
// 异常: 无
func (as ApprovalStatus) IsValid() bool {
	// 实现步骤:
	// 1. 检查审批状态是否在有效范围内

	return as >= ApprovalStatusPending && as <= ApprovalStatusApproved
}
