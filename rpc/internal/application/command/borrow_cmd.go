package command

import "time"

// CreateBorrowRecordCmd 创建借阅记录命令
// 使用建造者模式构建，确保命令对象的完整性和一致性
type CreateBorrowRecordCmd struct {
	userID            string               // 借阅用户ID
	documents         []BorrowDocumentItem // 借阅的文档列表
	borrowTime        time.Time            // 借阅时间
	dueTime           time.Time            // 应还时间
	borrowReason      string               // 借阅原因
	borrowApplyTime   time.Time            // 申请时间
	approvalStatus    int32                // 审批状态
	approvalInfo      map[string]any       // 审批信息
	approvalApplyTime time.Time            // 审批申请时间
}

// CreateBorrowRecordCmdBuilder 创建借阅记录命令建造者
type CreateBorrowRecordCmdBuilder struct {
	cmd *CreateBorrowRecordCmd
}

// NewCreateBorrowRecordCmdBuilder 创建建造者实例
// 功能: 初始化建造者，设置默认值
// 返回值: *CreateBorrowRecordCmdBuilder 建造者实例
func NewCreateBorrowRecordCmdBuilder() *CreateBorrowRecordCmdBuilder {
	return &CreateBorrowRecordCmdBuilder{
		cmd: &CreateBorrowRecordCmd{
			documents:      make([]BorrowDocumentItem, 0),
			approvalInfo:   make(map[string]any),
			approvalStatus: 1, // 默认待提交状态
		},
	}
}

// WithUserID 设置借阅用户ID
func (b *CreateBorrowRecordCmdBuilder) WithUserID(userID string) *CreateBorrowRecordCmdBuilder {
	b.cmd.userID = userID
	return b
}

// WithDocuments 设置借阅文档列表
func (b *CreateBorrowRecordCmdBuilder) WithDocuments(documents []BorrowDocumentItem) *CreateBorrowRecordCmdBuilder {
	b.cmd.documents = documents
	return b
}

// WithBorrowTime 设置借阅时间
func (b *CreateBorrowRecordCmdBuilder) WithBorrowTime(borrowTime time.Time) *CreateBorrowRecordCmdBuilder {
	b.cmd.borrowTime = borrowTime
	return b
}

// WithDueTime 设置应还时间
func (b *CreateBorrowRecordCmdBuilder) WithDueTime(dueTime time.Time) *CreateBorrowRecordCmdBuilder {
	b.cmd.dueTime = dueTime
	return b
}

// WithBorrowReason 设置借阅原因
func (b *CreateBorrowRecordCmdBuilder) WithBorrowReason(borrowReason string) *CreateBorrowRecordCmdBuilder {
	b.cmd.borrowReason = borrowReason
	return b
}

// WithBorrowApplyTime 设置申请时间
func (b *CreateBorrowRecordCmdBuilder) WithBorrowApplyTime(borrowApplyTime time.Time) *CreateBorrowRecordCmdBuilder {
	b.cmd.borrowApplyTime = borrowApplyTime
	return b
}

// WithApprovalStatus 设置审批状态
func (b *CreateBorrowRecordCmdBuilder) WithApprovalStatus(approvalStatus int32) *CreateBorrowRecordCmdBuilder {
	b.cmd.approvalStatus = approvalStatus
	return b
}

// WithApprovalInfo 设置审批信息
func (b *CreateBorrowRecordCmdBuilder) WithApprovalInfo(approvalInfo map[string]any) *CreateBorrowRecordCmdBuilder {
	b.cmd.approvalInfo = approvalInfo
	return b
}

// WithApprovalApplyTime 设置审批申请时间
func (b *CreateBorrowRecordCmdBuilder) WithApprovalApplyTime(approvalApplyTime time.Time) *CreateBorrowRecordCmdBuilder {
	b.cmd.approvalApplyTime = approvalApplyTime
	return b
}

// Build 构建命令对象
// 功能: 验证必要字段并构建最终的命令对象
// 返回值: *CreateBorrowRecordCmd 构建完成的命令对象
// 异常: 如果必要字段缺失会panic
func (b *CreateBorrowRecordCmdBuilder) Build() *CreateBorrowRecordCmd {
	// 验证必要字段
	if b.cmd.userID == "" {
		panic("userID is required")
	}
	if len(b.cmd.documents) == 0 {
		panic("documents is required")
	}
	if b.cmd.borrowTime.IsZero() {
		panic("borrowTime is required")
	}
	if b.cmd.dueTime.IsZero() {
		panic("dueTime is required")
	}
	if b.cmd.borrowReason == "" {
		panic("borrowReason is required")
	}

	return b.cmd
}

// Getter方法 - 提供对私有字段的访问

// UserID 获取借阅用户ID
func (c *CreateBorrowRecordCmd) UserID() string {
	return c.userID
}

// Documents 获取借阅文档列表
func (c *CreateBorrowRecordCmd) Documents() []BorrowDocumentItem {
	return c.documents
}

// BorrowTime 获取借阅时间
func (c *CreateBorrowRecordCmd) BorrowTime() time.Time {
	return c.borrowTime
}

// DueTime 获取应还时间
func (c *CreateBorrowRecordCmd) DueTime() time.Time {
	return c.dueTime
}

// BorrowReason 获取借阅原因
func (c *CreateBorrowRecordCmd) BorrowReason() string {
	return c.borrowReason
}

// BorrowApplyTime 获取申请时间
func (c *CreateBorrowRecordCmd) BorrowApplyTime() time.Time {
	return c.borrowApplyTime
}

// ApprovalStatus 获取审批状态
func (c *CreateBorrowRecordCmd) ApprovalStatus() int32 {
	return c.approvalStatus
}

// ApprovalInfo 获取审批信息
func (c *CreateBorrowRecordCmd) ApprovalInfo() map[string]any {
	return c.approvalInfo
}

// ApprovalApplyTime 获取审批申请时间
func (c *CreateBorrowRecordCmd) ApprovalApplyTime() time.Time {
	return c.approvalApplyTime
}

// BorrowDocumentItem 借阅文档项
// 使用建造者模式构建，确保文档项的完整性
type BorrowDocumentItem struct {
	documentID    string    // 文档ID
	versionNo     string    // 文档版本号
	moduleType    int32     // 文档所属模块，1书籍 | 2内部文档 | 3外部文档
	borrowStatus  int32     // 文档借阅状态
	recoverUserID string    // 回收人ID
	recoverTime   time.Time // 回收时间
}

// BorrowDocumentItemBuilder 借阅文档项建造者
type BorrowDocumentItemBuilder struct {
	item *BorrowDocumentItem
}

// NewBorrowDocumentItemBuilder 创建文档项建造者实例
// 功能: 初始化建造者，设置默认值
// 返回值: *BorrowDocumentItemBuilder 建造者实例
func NewBorrowDocumentItemBuilder() *BorrowDocumentItemBuilder {
	return &BorrowDocumentItemBuilder{
		item: &BorrowDocumentItem{
			borrowStatus: 0, // 默认未开始借阅状态
		},
	}
}

// WithDocumentID 设置文档ID
func (b *BorrowDocumentItemBuilder) WithDocumentID(documentID string) *BorrowDocumentItemBuilder {
	b.item.documentID = documentID
	return b
}

// WithVersionNo 设置文档版本号
func (b *BorrowDocumentItemBuilder) WithVersionNo(versionNo string) *BorrowDocumentItemBuilder {
	b.item.versionNo = versionNo
	return b
}

// WithModuleType 设置文档所属模块
func (b *BorrowDocumentItemBuilder) WithModuleType(moduleType int32) *BorrowDocumentItemBuilder {
	b.item.moduleType = moduleType
	return b
}

// WithBorrowStatus 设置文档借阅状态
func (b *BorrowDocumentItemBuilder) WithBorrowStatus(borrowStatus int32) *BorrowDocumentItemBuilder {
	b.item.borrowStatus = borrowStatus
	return b
}

// WithRecoverUserID 设置回收人ID
func (b *BorrowDocumentItemBuilder) WithRecoverUserID(recoverUserID string) *BorrowDocumentItemBuilder {
	b.item.recoverUserID = recoverUserID
	return b
}

// WithRecoverTime 设置回收时间
func (b *BorrowDocumentItemBuilder) WithRecoverTime(recoverTime time.Time) *BorrowDocumentItemBuilder {
	b.item.recoverTime = recoverTime
	return b
}

// Build 构建文档项对象
// 功能: 验证必要字段并构建最终的文档项对象
// 返回值: BorrowDocumentItem 构建完成的文档项对象
// 异常: 如果必要字段缺失会panic
func (b *BorrowDocumentItemBuilder) Build() BorrowDocumentItem {
	// 验证必要字段
	if b.item.documentID == "" {
		panic("documentID is required")
	}
	if b.item.versionNo == "" {
		panic("versionNo is required")
	}
	if b.item.moduleType == 0 {
		panic("moduleType is required")
	}

	return *b.item
}

// Getter方法 - 提供对私有字段的访问

// DocumentID 获取文档ID
func (d *BorrowDocumentItem) DocumentID() string {
	return d.documentID
}

// VersionNo 获取文档版本号
func (d *BorrowDocumentItem) VersionNo() string {
	return d.versionNo
}

// ModuleType 获取文档所属模块
func (d *BorrowDocumentItem) ModuleType() int32 {
	return d.moduleType
}

// BorrowStatus 获取文档借阅状态
func (d *BorrowDocumentItem) BorrowStatus() int32 {
	return d.borrowStatus
}

// RecoverUserID 获取回收人ID
func (d *BorrowDocumentItem) RecoverUserID() string {
	return d.recoverUserID
}

// RecoverTime 获取回收时间
func (d *BorrowDocumentItem) RecoverTime() time.Time {
	return d.recoverTime
}

// ModifyBorrowRecordCmd 修改借阅记录命令
// 使用建造者模式构建，确保命令对象的完整性和一致性
type ModifyBorrowRecordCmd struct {
	borrowRecordID    string               // 借阅记录ID
	userID            string               // 借阅用户ID
	documents         []BorrowDocumentItem // 借阅的文档列表
	borrowTime        time.Time            // 借阅时间
	dueTime           time.Time            // 应还时间
	borrowReason      string               // 借阅原因
	borrowApplyTime   time.Time            // 申请时间
	approvalStatus    int32                // 审批状态
	approvalInfo      map[string]any       // 审批信息
	approvalApplyTime time.Time            // 审批申请时间
}

// ModifyBorrowRecordCmdBuilder 修改借阅记录命令建造者
type ModifyBorrowRecordCmdBuilder struct {
	cmd *ModifyBorrowRecordCmd
}

// NewModifyBorrowRecordCmdBuilder 创建建造者实例
// 功能: 初始化建造者，设置默认值
// 返回值: *ModifyBorrowRecordCmdBuilder 建造者实例
func NewModifyBorrowRecordCmdBuilder() *ModifyBorrowRecordCmdBuilder {
	return &ModifyBorrowRecordCmdBuilder{
		cmd: &ModifyBorrowRecordCmd{
			documents:    make([]BorrowDocumentItem, 0),
			approvalInfo: make(map[string]any),
		},
	}
}

// WithBorrowRecordID 设置借阅记录ID
func (b *ModifyBorrowRecordCmdBuilder) WithBorrowRecordID(borrowRecordID string) *ModifyBorrowRecordCmdBuilder {
	b.cmd.borrowRecordID = borrowRecordID
	return b
}

// WithUserID 设置借阅用户ID
func (b *ModifyBorrowRecordCmdBuilder) WithUserID(userID string) *ModifyBorrowRecordCmdBuilder {
	b.cmd.userID = userID
	return b
}

// WithDocuments 设置借阅文档列表
func (b *ModifyBorrowRecordCmdBuilder) WithDocuments(documents []BorrowDocumentItem) *ModifyBorrowRecordCmdBuilder {
	b.cmd.documents = documents
	return b
}

// WithBorrowTime 设置借阅时间
func (b *ModifyBorrowRecordCmdBuilder) WithBorrowTime(borrowTime time.Time) *ModifyBorrowRecordCmdBuilder {
	b.cmd.borrowTime = borrowTime
	return b
}

// WithDueTime 设置应还时间
func (b *ModifyBorrowRecordCmdBuilder) WithDueTime(dueTime time.Time) *ModifyBorrowRecordCmdBuilder {
	b.cmd.dueTime = dueTime
	return b
}

// WithBorrowReason 设置借阅原因
func (b *ModifyBorrowRecordCmdBuilder) WithBorrowReason(borrowReason string) *ModifyBorrowRecordCmdBuilder {
	b.cmd.borrowReason = borrowReason
	return b
}

// WithBorrowApplyTime 设置申请时间
func (b *ModifyBorrowRecordCmdBuilder) WithBorrowApplyTime(borrowApplyTime time.Time) *ModifyBorrowRecordCmdBuilder {
	b.cmd.borrowApplyTime = borrowApplyTime
	return b
}

// WithApprovalStatus 设置审批状态
func (b *ModifyBorrowRecordCmdBuilder) WithApprovalStatus(approvalStatus int32) *ModifyBorrowRecordCmdBuilder {
	b.cmd.approvalStatus = approvalStatus
	return b
}

// WithApprovalInfo 设置审批信息
func (b *ModifyBorrowRecordCmdBuilder) WithApprovalInfo(approvalInfo map[string]any) *ModifyBorrowRecordCmdBuilder {
	b.cmd.approvalInfo = approvalInfo
	return b
}

// WithApprovalApplyTime 设置审批申请时间
func (b *ModifyBorrowRecordCmdBuilder) WithApprovalApplyTime(approvalApplyTime time.Time) *ModifyBorrowRecordCmdBuilder {
	b.cmd.approvalApplyTime = approvalApplyTime
	return b
}

// Build 构建命令对象
// 功能: 验证必要字段并构建最终的命令对象
// 返回值: *ModifyBorrowRecordCmd 构建完成的命令对象
// 异常: 如果必要字段缺失会panic
func (b *ModifyBorrowRecordCmdBuilder) Build() *ModifyBorrowRecordCmd {
	// 验证必要字段
	if b.cmd.borrowRecordID == "" {
		panic("borrowRecordID is required")
	}
	if b.cmd.userID == "" {
		panic("userID is required")
	}

	return b.cmd
}

// Getter方法 - 提供对私有字段的访问

// BorrowRecordID 获取借阅记录ID
func (c *ModifyBorrowRecordCmd) BorrowRecordID() string {
	return c.borrowRecordID
}

// UserID 获取借阅用户ID
func (c *ModifyBorrowRecordCmd) UserID() string {
	return c.userID
}

// Documents 获取借阅文档列表
func (c *ModifyBorrowRecordCmd) Documents() []BorrowDocumentItem {
	return c.documents
}

// BorrowTime 获取借阅时间
func (c *ModifyBorrowRecordCmd) BorrowTime() time.Time {
	return c.borrowTime
}

// DueTime 获取应还时间
func (c *ModifyBorrowRecordCmd) DueTime() time.Time {
	return c.dueTime
}

// BorrowReason 获取借阅原因
func (c *ModifyBorrowRecordCmd) BorrowReason() string {
	return c.borrowReason
}

// BorrowApplyTime 获取申请时间
func (c *ModifyBorrowRecordCmd) BorrowApplyTime() time.Time {
	return c.borrowApplyTime
}

// ApprovalStatus 获取审批状态
func (c *ModifyBorrowRecordCmd) ApprovalStatus() int32 {
	return c.approvalStatus
}

// ApprovalInfo 获取审批信息
func (c *ModifyBorrowRecordCmd) ApprovalInfo() map[string]any {
	return c.approvalInfo
}

// ApprovalApplyTime 获取审批申请时间
func (c *ModifyBorrowRecordCmd) ApprovalApplyTime() time.Time {
	return c.approvalApplyTime
}
