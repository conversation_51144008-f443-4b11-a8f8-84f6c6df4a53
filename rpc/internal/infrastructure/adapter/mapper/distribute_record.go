package mapper

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const (
	TableNameDistributeRecord = "distribute_records"
)

// DistributeRecord 对应 distribute_records 表
type DistributeRecord struct {
	ID                 string         `gorm:"type:varchar(64);primary_key"`
	WorkflowID         string         `gorm:"type:varchar(64);comment:'流程id'"`
	Applicant          string         `gorm:"type:varchar(64);comment:'申请人,用户id'"`
	ApplyDate          int64          `gorm:"comment:'申请日期'"`
	DistributeType     int32          `gorm:"comment:'发放类型，1内部发放 | 2外部发放'"`
	FileType           int32          `gorm:"comment:'文件类型，1内部文件 | 2外部文件'"`
	FileCategory       string         `gorm:"type:varchar(255);comment:'文件类别'"`
	TypeDictNodeId     string         `gorm:"type:varchar(64);comment:'类型字典节点id'"`
	Reason             string         `gorm:"type:text;comment:'原因'"`
	OtherReason        string         `gorm:"type:text;comment:'其他原因'"`
	WishDistributeDate int64          `gorm:"comment:'期望发放日期'"`
	DistributeCount    int            `gorm:"comment:'发放份数'"`
	SignForCount       int            `gorm:"comment:'签收份数'"`
	DisposeCount       int            `gorm:"comment:'处置份数'"`
	Status             int            `gorm:"comment:'状态,1待提交 | 2待审批 | 3已审批 | 4已驳回'"`
	ApprovalInfo       datatypes.JSON `gorm:"type:json;comment:'审批信息'"`
	CreatedAt          time.Time      `gorm:"column:created_at"` // 创建时间
	UpdatedAt          time.Time      `gorm:"column:updated_at"` // 更新时间
	CreatedBy          string         `gorm:"type:varchar(64);column:created_by"`
	UpdatedBy          string         `gorm:"type:varchar(64);column:updated_by"`
}

func (DistributeRecord) TableName() string {
	return TableNameDistributeRecord
}

// DistributeRecordClient 是 distribute_records 表的数据访问客户端
type DistributeRecordClient struct {
	db *gorm.DB
}

// NewDistributeRecordClient 创建一个新的 DistributeRecordClient 实例
func NewDistributeRecordClient(db *DocvaultDB) *DistributeRecordClient {
	return &DistributeRecordClient{
		db: db.GetDB(),
	}
}

// Create 创建一个新的 DistributeRecord 记录
func (c *DistributeRecordClient) CreateWithTx(ctx context.Context, tx Transaction, record *DistributeRecord) error {
	if err := tx.GetTX().WithContext(ctx).Create(record).Error; err != nil {
		logc.Error(ctx, "Failed to create distribute record", err)
		return err
	}
	return nil
}

// GetByID 根据 DistributeID 获取 DistributeRecord 记录
func (c *DistributeRecordClient) GetByID(ctx context.Context, id string) (*DistributeRecord, error) {
	var record DistributeRecord
	if err := c.db.WithContext(ctx).Where("id = ?", id).First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logc.Error(ctx, "Failed to get distribute record by DistributeID", err)
		return nil, err
	}
	return &record, nil
}

// GetByWorkflowID 根据 WorkflowID 获取 DistributeRecord 记录
// 参数:
//   - ctx: 上下文
//   - workflowID: 工作流ID
//
// 返回:
//   - *DistributeRecord: 分发记录，如果未找到返回nil
//   - error: 错误信息
func (c *DistributeRecordClient) GetByWorkflowID(ctx context.Context, workflowID string) (*DistributeRecord, error) {
	var record DistributeRecord
	if err := c.db.WithContext(ctx).Where("workflow_id = ?", workflowID).First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logc.Error(ctx, "Failed to get distribute record by WorkflowID", err)
		return nil, err
	}
	return &record, nil
}

// Update 更新 DistributeRecord 记录
func (c *DistributeRecordClient) Update(ctx context.Context, record *DistributeRecord) error {
	if err := c.db.WithContext(ctx).Save(record).Error; err != nil {
		logc.Error(ctx, "Failed to update distribute record", err)
		return err
	}
	return nil
}

// UpdateWithTx 更新 DistributeRecord 记录（事务版本）
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - record: 要更新的发放记录
//
// 返回:
//   - error: 更新失败时返回错误，成功时返回nil
func (c *DistributeRecordClient) UpdateWithTx(ctx context.Context, tx Transaction, record *DistributeRecord) error {
	if err := tx.GetTX().WithContext(ctx).Save(record).Error; err != nil {
		logc.Error(ctx, "Failed to update distribute record with transaction", err)
		return err
	}
	return nil
}

// UpdateStatus 根据 ID 更新 DistributeRecord 记录的状态
func (c *DistributeRecordClient) UpdateStatusByWorkflowId(ctx context.Context, workflowId string, status int32) error {
	if err := c.db.WithContext(ctx).Model(&DistributeRecord{}).Where("workflow_id = ?", workflowId).Update("status", status).Error; err != nil {
		logc.Error(ctx, "Failed to update distribute record status", err)
		return err
	}
	return nil
}

// DeleteByIDWithTx 根据ID删除发放记录（事务版本）
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - id: 发放记录ID
//
// 返回:
//   - error: 删除失败时返回错误，成功时返回nil
func (c *DistributeRecordClient) DeleteByIDWithTx(ctx context.Context, tx Transaction, id string) error {
	if err := tx.GetTX().WithContext(ctx).Where("id = ?", id).Delete(&DistributeRecord{}).Error; err != nil {
		logc.Error(ctx, "Failed to delete distribute record by ID", err)
		return err
	}
	return nil
}

// GetDistributesRequest 查询分发记录的请求参数
type GetDistributesRequest struct {
	FileNumber     string   `json:"fileNumber"`     // 文件编号，可选过滤条件
	FileName       string   `json:"fileName"`       // 文件名称，可选过滤条件
	FileType       int32    `json:"fileType"`       // 文件类型，可选过滤条件
	FileCategory   []string `json:"fileCategory"`   // 文件类别列表，可选过滤条件
	DistributeType int32    `json:"distributeType"` // 发放类型，可选过滤条件
	Status         int32    `json:"status"`         // 状态，可选过滤条件
	Applicant      []string `json:"applicant"`      // 申请人列表，可选过滤条件
	Page           int      `json:"offset"`         // 分页偏移量
	PageSize       int      `json:"limit"`          // 分页限制数量
	NoPage         bool     `json:"noPage"`         // 是否不分页
}

// GetDistributes 根据条件查询分发记录列表
// 参数:
//   - ctx: 上下文对象，用于传递请求范围的值、取消信号和截止时间
//   - req: 查询请求参数，包含所有过滤条件和分页信息
//
// 返回:
//   - []*DistributeRecord: 分发记录列表
//   - int64: 总记录数
//   - error: 查询失败时返回错误，成功时返回nil
func (c *DistributeRecordClient) GetDistributes(ctx context.Context, req *GetDistributesRequest) ([]*DistributeRecord, int64, error) {
	query := c.db.WithContext(ctx).Model(&DistributeRecord{})

	// 构建查询条件
	if req.FileNumber != "" {
		// 通过关联文件表查询文件编号
		query = query.Joins("JOIN distribute_record_files ON distribute_records.id = distribute_record_files.record_id").
			Where("distribute_record_files.number LIKE ?", "%"+req.FileNumber+"%")
	}

	if req.FileName != "" {
		// 通过关联文件表查询文件名称
		query = query.Joins("JOIN distribute_record_files ON distribute_records.id = distribute_record_files.record_id").
			Where("distribute_record_files.file_name LIKE ?", "%"+req.FileName+"%")
	}

	if req.FileType > 0 {
		query = query.Where("file_type = ?", req.FileType)
	}

	if len(req.FileCategory) > 0 {
		query = query.Where("file_category IN ?", req.FileCategory)
	}

	if req.DistributeType > 0 {
		query = query.Where("distribute_type = ?", req.DistributeType)
	}

	if req.Status > 0 {
		query = query.Where("status = ?", req.Status)
	}

	if len(req.Applicant) > 0 {
		query = query.Where("applicant IN ?", req.Applicant)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logc.Error(ctx, "Failed to count distribute records", err)
		return nil, 0, err
	}

	// 获取分页数据
	var records []*DistributeRecord
	dataQuery := query
	if !req.NoPage {
		dataQuery = dataQuery.Offset((req.Page - 1) * req.PageSize).Limit(req.PageSize)
	}
	if err := dataQuery.Order("created_at DESC").Find(&records).Error; err != nil {
		logc.Error(ctx, "Failed to get distribute records list", err)
		return nil, 0, err
	}

	return records, total, nil
}
