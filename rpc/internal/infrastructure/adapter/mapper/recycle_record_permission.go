package mapper

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const (
	TableNameRecycleRecordPermission = "recycle_record_permissions"
)

// RecycleRecordPermission 对应 recycle_record_permissions 表
type RecycleRecordPermission struct {
	ID             string `gorm:"type:varchar(64);primary_key"`
	FileRecordID   string `gorm:"type:varchar(64);index;comment:'关联RecycleRecordFile的ID'"`
	UserID         string `gorm:"type:varchar(64);comment:'接收人ID'"`
	FileForm       int32  `gorm:"comment:'文件形式,1电子文件 | 2纸质文件'"`
	FilePermission int32  `gorm:"comment:'文件权限,1查阅 | 2查阅/下载 | 3一次下载'"`
}

func (RecycleRecordPermission) TableName() string {
	return TableNameRecycleRecordPermission
}

// RecycleRecordPermissionClient 是 recycle_record_permissions 表的数据访问客户端
type RecycleRecordPermissionClient struct {
	db *gorm.DB
}

// NewRecycleRecordPermissionClient 创建一个新的 RecycleRecordPermissionClient 实例
func NewRecycleRecordPermissionClient(db *DocvaultDB) *RecycleRecordPermissionClient {
	return &RecycleRecordPermissionClient{
		db: db.GetDB(),
	}
}

// CreateWithTx 创建一个新的 RecycleRecordPermission 记录
func (c *RecycleRecordPermissionClient) CreateWithTx(ctx context.Context, tx Transaction, permission *RecycleRecordPermission) error {
	if err := tx.GetTX().WithContext(ctx).Create(permission).Error; err != nil {
		logc.Error(ctx, "Failed to create recycle record permission", err)
		return err
	}
	return nil
}

// GetByFileRecordID 根据 FileRecordID 获取所有相关的 RecycleRecordPermission 记录
func (c *RecycleRecordPermissionClient) GetByFileRecordID(ctx context.Context, fileRecordID string) ([]*RecycleRecordPermission, error) {
	var permissions []*RecycleRecordPermission
	if err := c.db.WithContext(ctx).Where("file_record_id = ?", fileRecordID).Find(&permissions).Error; err != nil {
		logc.Error(ctx, "Failed to get recycle record permissions by file record ID", err)
		return nil, err
	}
	return permissions, nil
}

// BatchCreateWithTx 批量创建 RecycleRecordPermission 记录
func (c *RecycleRecordPermissionClient) BatchCreateWithTx(ctx context.Context, tx Transaction, permissions []*RecycleRecordPermission) error {
	if len(permissions) == 0 {
		return nil
	}
	if err := tx.GetTX().WithContext(ctx).Create(&permissions).Error; err != nil {
		logc.Error(ctx, "Failed to batch create recycle record permissions", err)
		return err
	}
	return nil
}

// GetRecycleDocPermissionUsers 根据文件ID、文件来源和文件权限获取回收权限用户信息
// 改造说明：file_id在distribute_record_files中，回收状态在distribute_record_permissions中
// 参数:
//   - ctx: 上下文
//   - fileId: 文件ID
//   - fileFrom: 文件来源类型（1内部文件 | 2外部文件）
//   - filePermission: 文件权限（1查阅 | 2查阅/下载 | 3一次下载）
//
// 返回:
//   - []struct: 用户ID和回收状态的结构体列表
//   - error: 错误信息，成功时返回nil
func (c *RecycleRecordPermissionClient) GetRecycleDocPermissionUsers(ctx context.Context, fileId string, fileFrom, filePermission int32) ([]struct {
	UserID        string `gorm:"column:user_id"`
	RecycleStatus int    `gorm:"column:recycle_status"`
}, error) {
	var result []struct {
		UserID        string `gorm:"column:user_id"`
		RecycleStatus int    `gorm:"column:recycle_status"`
	}

	// 改造后的查询逻辑：
	// 1. file_id 在 distribute_record_files 表中查找
	// 2. 回收状态从 distribute_record_permissions 表中获取 dispose_status 字段
	// 3. 通过 distribute_record_files 和 distribute_record_permissions 的关联查询
	err := c.db.WithContext(ctx).
		Table("distribute_record_permissions as drp").
		Select("drp.user_id, drp.dispose_status as recycle_status").
		Joins("LEFT JOIN distribute_record_files as drf ON drp.file_record_id = drf.id").
		Joins("LEFT JOIN distribute_records as dr ON drf.record_id = dr.id").
		Where("drf.file_id = ? AND dr.file_type = ? AND drp.file_permission = ?",
			fileId, fileFrom, filePermission).
		Group("drp.user_id, drp.dispose_status").
		Find(&result).Error

	if err != nil {
		logc.Error(ctx, "根据文件ID、文件来源和文件权限获取回收权限用户信息失败", err)
		return nil, err
	}

	logc.Infof(ctx, "根据文件ID[%s]、文件来源[%d]、文件权限[%d]查询到回收权限用户，数量: %d", fileId, fileFrom, filePermission, len(result))
	return result, nil
}

// GetUserRecycleStatus 批量查询用户的回收状态和回收时间
// 参数:
//   - ctx: 上下文
//   - distributeRecordID: 发放记录ID
//   - userIDs: 用户ID列表
//
// 返回:
//   - map[string]*UserRecycleInfo: 用户ID到回收信息的映射
//   - error: 错误信息
func (c *RecycleRecordPermissionClient) GetUserRecycleStatus(ctx context.Context, distributeRecordID string, userIDs []string) (map[string]*UserRecycleInfo, error) {
	if len(userIDs) == 0 {
		return make(map[string]*UserRecycleInfo), nil
	}

	var results []struct {
		UserID      string    `gorm:"column:user_id"`
		RecycleDate time.Time `gorm:"column:recycle_date"`
	}

	// 查询已回收的用户信息
	err := c.db.WithContext(ctx).
		Table("recycle_record_permissions as rp").
		Select("rp.user_id, rr.recycle_date").
		Joins("LEFT JOIN recycle_record_files as rf ON rp.file_record_id = rf.id").
		Joins("LEFT JOIN recycle_records as rr ON rf.record_id = rr.id").
		Where("rr.distribute_record_id = ? AND rp.user_id IN ?", distributeRecordID, userIDs).
		Group("rp.user_id, rr.recycle_date").
		Find(&results).Error

	if err != nil {
		logc.Error(ctx, "Failed to get user recycle status", err)
		return nil, err
	}

	// 构建结果映射
	recycleInfoMap := make(map[string]*UserRecycleInfo)

	// 初始化所有用户为未回收状态
	for _, userID := range userIDs {
		recycleInfoMap[userID] = &UserRecycleInfo{
			UserID:        userID,
			RecycleStatus: 0, // 0未回收
			RecycleTime:   0,
		}
	}

	// 更新已回收用户的状态
	for _, result := range results {
		// 将time.Time转换为毫秒级时间戳
		recycleTimeMs := int64(0)
		if !result.RecycleDate.IsZero() {
			recycleTimeMs = result.RecycleDate.UnixMilli()
		}

		recycleInfoMap[result.UserID] = &UserRecycleInfo{
			UserID:        result.UserID,
			RecycleStatus: 1, // 1已回收
			RecycleTime:   recycleTimeMs,
		}
	}

	return recycleInfoMap, nil
}

// UserRecycleInfo 用户回收信息
type UserRecycleInfo struct {
	UserID        string // 用户ID
	RecycleStatus int32  // 回收状态（0未回收 | 1已回收 | 2待审批）
	RecycleTime   int64  // 回收时间（Unix时间戳）
}
