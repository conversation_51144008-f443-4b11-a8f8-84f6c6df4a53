package mapper

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const (
	TableNameBorrowRecord = "borrow_records"
)

// BorrowRecord 对应 borrow_records 表
// 重构后的借阅记录表，移除了DocumentID和ModuleType字段，这些信息现在存储在关系表中
type BorrowRecord struct {
	ID                string         `gorm:"type:varchar(64);primary_key"`
	UserID            string         `gorm:"type:varchar(64);index;comment:'借阅用户ID'"`
	BorrowTime        time.Time      `gorm:"comment:'借阅时间'"`
	DueTime           time.Time      `gorm:"comment:'应还时间'"`
	BorrowReason      string         `gorm:"type:varchar(256);comment:'借阅原因'"`
	BorrowApplyTime   time.Time      `gorm:"comment:'申请时间'"`
	ApprovalStatus    int32          `gorm:"comment:'审批状态'"`
	ApprovalInfo      map[string]any `gorm:"serializer:json;type:json;comment:'审批信息'"`
	ApprovalApplyTime time.Time      `gorm:"comment:'审批申请时间'"`
	CreatedAt         time.Time      `gorm:"column:created_at"` // 创建时间
	UpdatedAt         time.Time      `gorm:"column:updated_at"` // 更新时间
	CreatedBy         string         `gorm:"type:varchar(64);column:created_by"`
	UpdatedBy         string         `gorm:"type:varchar(64);column:updated_by"`
}

func (BorrowRecord) TableName() string {
	return TableNameBorrowRecord
}

// BorrowRecordClient 是 borrow_records 表的数据访问客户端
type BorrowRecordClient struct {
	db *gorm.DB
}

// NewBorrowRecordClient 创建一个新的 BorrowRecordClient 实例
// 功能: 创建并初始化借阅记录数据访问客户端
// 参数:
//
//	db: DocvaultDB数据库连接实例
//
// 返回值:
//
//	*BorrowRecordClient: 借阅记录客户端实例
//
// 异常: 无
func NewBorrowRecordClient(db *DocvaultDB) *BorrowRecordClient {
	return &BorrowRecordClient{
		db: db.GetDB(),
	}
}

// Create 创建一个新的 BorrowRecord 记录
// 功能: 创建借阅记录
// 参数:
//
//	ctx: 上下文对象
//	record: 借阅记录对象
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *BorrowRecordClient) Create(ctx context.Context, record BorrowRecord) error {
	// 实现步骤:
	// 1. 创建借阅记录
	if err := c.db.WithContext(ctx).Create(&record).Error; err != nil {
		logc.Error(ctx, "Failed to create borrow record", err)
		return err
	}
	return nil
}

// CreateWithTX 在事务中创建一个新的 BorrowRecord 记录
// 功能: 在事务中创建借阅记录
// 参数:
//
//	ctx: 上下文对象
//	tx: 事务对象
//	record: 借阅记录对象
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *BorrowRecordClient) CreateWithTX(ctx context.Context, tx Transaction, record *BorrowRecord) error {
	// 实现步骤:
	// 1. 在事务中创建借阅记录
	if err := tx.GetTX().WithContext(ctx).Create(record).Error; err != nil {
		logc.Error(ctx, "Failed to create borrow record with transaction", err)
		return err
	}
	return nil
}

// GetByID 根据 ID 获取 BorrowRecord 记录
// 功能: 根据主键ID查询借阅记录
// 参数:
//
//	ctx: 上下文对象
//	id: 借阅记录ID
//
// 返回值:
//
//	*BorrowRecord: 借阅记录对象，未找到时返回nil
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *BorrowRecordClient) GetByID(ctx context.Context, id string) (*BorrowRecord, error) {
	// 实现步骤:
	// 1. 根据ID查询借阅记录
	// 2. 处理记录不存在的情况
	var record BorrowRecord
	if err := c.db.WithContext(ctx).Where("id = ?", id).First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logc.Error(ctx, "Failed to get borrow record by ID", err)
		return nil, err
	}
	return &record, nil
}

// Update 更新 BorrowRecord 记录
// 功能: 更新借阅记录信息
// 参数:
//
//	ctx: 上下文对象
//	record: 借阅记录对象
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *BorrowRecordClient) Update(ctx context.Context, record *BorrowRecord) error {
	// 实现步骤:
	// 1. 保存借阅记录更新
	if err := c.db.WithContext(ctx).Save(record).Error; err != nil {
		logc.Error(ctx, "Failed to update borrow record", err)
		return err
	}
	return nil
}

// UpdateWithTX 在事务中更新 BorrowRecord 记录
// 功能: 在事务中更新借阅记录信息
// 参数:
//
//	ctx: 上下文对象
//	tx: 事务对象
//	record: 借阅记录对象
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *BorrowRecordClient) UpdateWithTX(ctx context.Context, tx Transaction, record *BorrowRecord) error {
	// 实现步骤:
	// 1. 在事务中保存借阅记录更新
	if err := tx.GetTX().WithContext(ctx).Save(record).Error; err != nil {
		logc.Error(ctx, "Failed to update borrow record with transaction", err)
		return err
	}
	return nil
}

// GetByUserID 根据用户ID获取借阅记录列表
// 功能: 查询指定用户的所有借阅记录
// 参数:
//
//	ctx: 上下文对象
//	userID: 用户ID
//
// 返回值:
//
//	[]BorrowRecord: 借阅记录列表
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *BorrowRecordClient) GetByUserID(ctx context.Context, userID string) ([]BorrowRecord, error) {
	// 实现步骤:
	// 1. 根据用户ID查询借阅记录列表
	var records []BorrowRecord
	if err := c.db.WithContext(ctx).Where("user_id = ?", userID).Find(&records).Error; err != nil {
		logc.Error(ctx, "Failed to get borrow records by user ID", err)
		return nil, err
	}
	return records, nil
}

// GetByDocumentID 根据文档ID获取借阅记录列表
// 功能: 查询指定文档的所有借阅记录
// 参数:
//
//	ctx: 上下文对象
//	documentID: 文档ID
//
// 返回值:
//
//	[]BorrowRecord: 借阅记录列表
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *BorrowRecordClient) GetByDocumentID(ctx context.Context, documentID string) ([]BorrowRecord, error) {
	// 实现步骤:
	// 1. 根据文档ID查询借阅记录列表
	var records []BorrowRecord
	if err := c.db.WithContext(ctx).Where("document_id = ?", documentID).Find(&records).Error; err != nil {
		logc.Error(ctx, "Failed to get borrow records by document ID", err)
		return nil, err
	}
	return records, nil
}

// GetOverdueRecords 获取过期的借阅记录
// 功能: 查询所有已过期且仍在借阅中的记录
// 参数:
//
//	ctx: 上下文对象
//
// 返回值:
//
//	[]BorrowRecord: 过期借阅记录列表
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *BorrowRecordClient) GetOverdueRecords(ctx context.Context) ([]BorrowRecord, error) {
	// 实现步骤:
	// 1. 查询应还时间小于当前时间且状态为借阅中的记录
	var records []BorrowRecord
	now := time.Now()
	if err := c.db.WithContext(ctx).Where("due_date < ? AND borrow_status = ?", now, 4).Find(&records).Error; err != nil {
		logc.Error(ctx, "Failed to get overdue borrow records", err)
		return nil, err
	}
	return records, nil
}
