package mapper

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const (
	TableNameRecycleRecord = "recycle_records"
)

// RecycleRecord 对应 recycle_records 表
type RecycleRecord struct {
	ID                 string         `gorm:"type:varchar(64);primary_key"`
	DistributeRecordID string         `gorm:"type:varchar(64);index;comment:'关联的发放记录ID'"`
	RecycleBy          string         `gorm:"type:varchar(64);comment:'回收人,用户id'"`
	RecycleDate        time.Time      `gorm:"column:recycle_date;comment:'回收日期'"`
	RecycleReason      string         `gorm:"type:text;comment:'回收原因'"`
	OtherReason        string         `gorm:"type:text;comment:'其他原因'"`
	WorkflowID         string         `gorm:"type:varchar(64);comment:'流程ID'"`
	ApprovalInfo       datatypes.JSON `gorm:"type:json;column:approval_info;comment:'审批信息'"`
	CreatedAt          time.Time      `gorm:"column:created_at"` // 创建时间
	UpdatedAt          time.Time      `gorm:"column:updated_at"` // 更新时间
	CreatedBy          string         `gorm:"type:varchar(64);column:created_by"`
	UpdatedBy          string         `gorm:"type:varchar(64);column:updated_by"`
}

func (RecycleRecord) TableName() string {
	return TableNameRecycleRecord
}

// RecycleRecordClient 是 recycle_records 表的数据访问客户端
type RecycleRecordClient struct {
	db *gorm.DB
}

// NewRecycleRecordClient 创建一个新的 RecycleRecordClient 实例
func NewRecycleRecordClient(db *DocvaultDB) *RecycleRecordClient {
	return &RecycleRecordClient{
		db: db.GetDB(),
	}
}

// CreateWithTx 创建一个新的 RecycleRecord 记录
func (c *RecycleRecordClient) CreateWithTx(ctx context.Context, tx Transaction, record *RecycleRecord) error {
	if err := tx.GetTX().WithContext(ctx).Create(record).Error; err != nil {
		logc.Error(ctx, "Failed to create recycle record", err)
		return err
	}
	return nil
}

// GetByID 根据 ID 获取 RecycleRecord 记录
func (c *RecycleRecordClient) GetByID(ctx context.Context, id string) (*RecycleRecord, error) {
	var record RecycleRecord
	if err := c.db.WithContext(ctx).Where("id = ?", id).First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logc.Error(ctx, "Failed to get recycle record by ID", err)
		return nil, err
	}
	return &record, nil
}

// Update 更新 RecycleRecord 记录
func (c *RecycleRecordClient) Update(ctx context.Context, record *RecycleRecord) error {
	if err := c.db.WithContext(ctx).Save(record).Error; err != nil {
		logc.Error(ctx, "Failed to update recycle record", err)
		return err
	}
	return nil
}

// GetByDistributeRecordID 根据发放记录ID获取回收记录
func (c *RecycleRecordClient) GetByDistributeRecordID(ctx context.Context, distributeRecordID string) (*RecycleRecord, error) {
	var record RecycleRecord
	if err := c.db.WithContext(ctx).Where("distribute_record_id = ?", distributeRecordID).First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logc.Error(ctx, "Failed to get recycle record by distribute record ID", err)
		return nil, err
	}
	return &record, nil
}
