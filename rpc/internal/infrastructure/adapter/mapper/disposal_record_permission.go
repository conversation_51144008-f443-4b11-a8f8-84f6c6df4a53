package mapper

import (
	"context"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const (
	TableNameDisposalRecordPermission = "disposal_record_permissions"
)

// DisposalRecordPermission 对应 disposal_record_permissions 表
type DisposalRecordPermission struct {
	ID             string `gorm:"type:varchar(64);primary_key"`
	FileRecordID   string `gorm:"type:varchar(64);index;comment:'关联DisposalRecordFile的ID'"`
	UserID         string `gorm:"type:varchar(64);comment:'接收人ID'"`
	FileForm       int32  `gorm:"comment:'文件形式,1电子文件 | 2纸质文件'"`
	FilePermission int32  `gorm:"comment:'文件权限,1查阅 | 2查阅/下载 | 3一次下载'"`
}

func (DisposalRecordPermission) TableName() string {
	return TableNameDisposalRecordPermission
}

// DisposalRecordPermissionClient 是 disposal_record_permissions 表的数据访问客户端
type DisposalRecordPermissionClient struct {
	db *gorm.DB
}

// NewDisposalRecordPermissionClient 创建一个新的 DisposalRecordPermissionClient 实例
func NewDisposalRecordPermissionClient(db *DocvaultDB) *DisposalRecordPermissionClient {
	return &DisposalRecordPermissionClient{
		db: db.GetDB(),
	}
}

// CreateWithTx 创建一个新的 DisposalRecordPermission 记录
func (c *DisposalRecordPermissionClient) CreateWithTx(ctx context.Context, tx Transaction, permission *DisposalRecordPermission) error {
	if err := tx.GetTX().WithContext(ctx).Create(permission).Error; err != nil {
		logc.Error(ctx, "Failed to create disposal record permission", err)
		return err
	}
	return nil
}

// GetByFileRecordID 根据 FileRecordID 获取所有相关的 DisposalRecordPermission 记录
func (c *DisposalRecordPermissionClient) GetByFileRecordID(ctx context.Context, fileRecordID string) ([]*DisposalRecordPermission, error) {
	var permissions []*DisposalRecordPermission
	if err := c.db.WithContext(ctx).Where("file_record_id = ?", fileRecordID).Find(&permissions).Error; err != nil {
		logc.Error(ctx, "Failed to get disposal record permissions by file record ID", err)
		return nil, err
	}
	return permissions, nil
}

// UpdateByConditions 根据条件批量更新处置记录权限
// 参数:
//   - ctx: 上下文
//   - fileRecordID: 文件记录ID
//   - fileForm: 文件形式
//   - filePermission: 文件权限
//   - userIDs: 用户ID列表
//
// 返回:
//   - error: 更新失败时返回错误，成功时返回nil
func (c *DisposalRecordPermissionClient) UpdateByConditions(ctx context.Context, fileRecordID string, fileForm, filePermission int32, userIDs []string) error {
	if len(userIDs) == 0 {
		return nil
	}

	// 更新现有记录的文件形式和文件权限
	err := c.db.WithContext(ctx).
		Model(&DisposalRecordPermission{}).
		Where("file_record_id = ? AND user_id IN ?", fileRecordID, userIDs).
		Updates(map[string]interface{}{
			"file_form":       fileForm,
			"file_permission": filePermission,
		}).Error

	if err != nil {
		logc.Error(ctx, "Failed to update disposal record permissions by conditions", err)
		return err
	}

	return nil
}

// BatchCreateWithTx 批量创建 DisposalRecordPermission 记录
func (c *DisposalRecordPermissionClient) BatchCreateWithTx(ctx context.Context, tx Transaction, permissions []*DisposalRecordPermission) error {
	if len(permissions) == 0 {
		return nil
	}
	if err := tx.GetTX().WithContext(ctx).Create(&permissions).Error; err != nil {
		logc.Error(ctx, "Failed to batch create disposal record permissions", err)
		return err
	}
	return nil
}
