package mapper

import (
	"context"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const (
	TableNameRecycleRecordFile = "recycle_record_files"
)

// RecycleRecordFile 对应 recycle_record_files 表
type RecycleRecordFile struct {
	ID                     string `gorm:"type:varchar(64);primary_key"`
	RecordID               string `gorm:"type:varchar(64);index;comment:'关联RecycleRecord的ID'"`
	DistributeRecordFileID string `gorm:"type:varchar(64);comment:'清单文件id'"`
}

func (RecycleRecordFile) TableName() string {
	return TableNameRecycleRecordFile
}

// RecycleRecordFileClient 是 recycle_record_files 表的数据访问客户端
type RecycleRecordFileClient struct {
	db *gorm.DB
}

// NewRecycleRecordFileClient 创建一个新的 RecycleRecordFileClient 实例
func NewRecycleRecordFileClient(db *DocvaultDB) *RecycleRecordFileClient {
	return &RecycleRecordFileClient{
		db: db.GetDB(),
	}
}

// CreateWithTx 创建一个新的 RecycleRecordFile 记录
func (c *RecycleRecordFileClient) CreateWithTx(ctx context.Context, tx Transaction, file *RecycleRecordFile) error {
	if err := tx.GetTX().WithContext(ctx).Create(file).Error; err != nil {
		logc.Error(ctx, "Failed to create recycle record file", err)
		return err
	}
	return nil
}

// BatchCreateWithTx 批量创建 RecycleRecordFile 记录
func (c *RecycleRecordFileClient) BatchCreateWithTx(ctx context.Context, tx Transaction, files []*RecycleRecordFile) error {
	if len(files) == 0 {
		return nil
	}
	if err := tx.GetTX().WithContext(ctx).Create(&files).Error; err != nil {
		logc.Error(ctx, "Failed to batch create recycle record files", err)
		return err
	}
	return nil
}

// GetByRecordID 根据 RecordID 获取所有相关的 RecycleRecordFile 记录
func (c *RecycleRecordFileClient) GetByRecordID(ctx context.Context, recordID string) ([]*RecycleRecordFile, error) {
	var files []*RecycleRecordFile
	if err := c.db.WithContext(ctx).Where("record_id = ?", recordID).Find(&files).Error; err != nil {
		logc.Error(ctx, "Failed to get recycle record files by record ID", err)
		return nil, err
	}
	return files, nil
}

// GetByID 根据 ID 获取 RecycleRecordFile 记录
func (c *RecycleRecordFileClient) GetByID(ctx context.Context, id string) (*RecycleRecordFile, error) {
	var file RecycleRecordFile
	if err := c.db.WithContext(ctx).Where("id = ?", id).First(&file).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logc.Error(ctx, "Failed to get recycle record file by ID", err)
		return nil, err
	}
	return &file, nil
}

// GetByDistributeRecordFileID 根据发放记录文件ID获取回收记录文件列表
// 参数:
//   - ctx: 上下文
//   - distributeRecordFileID: 发放记录文件ID
//
// 返回:
//   - []*RecycleRecordFile: 回收记录文件列表，如果未找到返回空列表
//   - error: 错误信息，成功时返回nil
//
// 业务逻辑:
//
//	根据发放记录文件ID查询所有相关的回收记录文件
func (c *RecycleRecordFileClient) GetByDistributeRecordFileID(ctx context.Context, distributeRecordFileID string) ([]*RecycleRecordFile, error) {
	var files []*RecycleRecordFile
	if err := c.db.WithContext(ctx).Where("distribute_record_file_id = ?", distributeRecordFileID).Find(&files).Error; err != nil {
		logc.Error(ctx, "Failed to get recycle record files by distribute record file ID", err)
		return nil, err
	}
	return files, nil
}
