package mapper

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const (
	TableNameBorrowDocumentRelation = "borrow_document_relations"
)

// BorrowDocumentRelation 借阅文档关系表
// 表示借阅记录与文档的关系，支持一个借阅记录对应多个文档
type BorrowDocumentRelation struct {
	ID             string     `gorm:"type:varchar(64);primary_key"`
	BorrowRecordID string     `gorm:"type:varchar(64);index;comment:'借阅记录ID'"`
	DocumentID     string     `gorm:"type:varchar(64);index;comment:'文档ID'"`
	VersionNo      string     `gorm:"type:varchar(32);comment:'文档版本号'"`
	ModuleType     int32      `gorm:"comment:'文档所属模块，1书籍 | 2内部文档 | 3外部文档'"`
	BorrowStatus   int32      `gorm:"comment:'文档借阅状态'"`
	RecoverUserID  string     `gorm:"type:varchar(64);comment:'回收用户ID'"`
	RecoverTime    *time.Time `gorm:"comment:'回收时间'"`
	CreatedAt      time.Time  `gorm:"column:created_at"` // 创建时间
	UpdatedAt      time.Time  `gorm:"column:updated_at"` // 更新时间
	CreatedBy      string     `gorm:"type:varchar(64);column:created_by"`
	UpdatedBy      string     `gorm:"type:varchar(64);column:updated_by"`
}

func (BorrowDocumentRelation) TableName() string {
	return TableNameBorrowDocumentRelation
}

// BorrowDocumentRelationClient 是 borrow_document_relations 表的数据访问客户端
type BorrowDocumentRelationClient struct {
	db *gorm.DB
}

// NewBorrowDocumentRelationClient 创建一个新的 BorrowDocumentRelationClient 实例
// 功能: 创建并初始化借阅文档关系数据访问客户端
// 参数:
//
//	db: DocvaultDB数据库连接实例
//
// 返回值:
//
//	*BorrowDocumentRelationClient: 借阅文档关系客户端实例
//
// 异常: 无
func NewBorrowDocumentRelationClient(db *DocvaultDB) *BorrowDocumentRelationClient {
	return &BorrowDocumentRelationClient{
		db: db.GetDB(),
	}
}

// Create 创建一个新的 BorrowDocumentRelation 记录
// 功能: 创建借阅文档关系记录
// 参数:
//
//	ctx: 上下文对象
//	relation: 借阅文档关系对象
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *BorrowDocumentRelationClient) Create(ctx context.Context, relation BorrowDocumentRelation) error {
	// 实现步骤:
	// 1. 创建借阅文档关系记录
	if err := c.db.WithContext(ctx).Create(&relation).Error; err != nil {
		logc.Error(ctx, "Failed to create borrow document relation", err)
		return err
	}
	return nil
}

// CreateBatch 批量创建 BorrowDocumentRelation 记录
// 功能: 批量创建借阅文档关系记录
// 参数:
//
//	ctx: 上下文对象
//	relations: 借阅文档关系对象列表
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *BorrowDocumentRelationClient) CreateBatch(ctx context.Context, relations []BorrowDocumentRelation) error {
	// 实现步骤:
	// 1. 批量创建借阅文档关系记录
	if err := c.db.WithContext(ctx).Create(&relations).Error; err != nil {
		logc.Error(ctx, "Failed to create borrow document relations in batch", err)
		return err
	}
	return nil
}

// CreateBatchWithTX 在事务中批量创建 BorrowDocumentRelation 记录
// 功能: 在事务中批量创建借阅文档关系记录
// 参数:
//
//	ctx: 上下文对象
//	tx: 事务对象
//	relations: 借阅文档关系对象列表
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *BorrowDocumentRelationClient) CreateBatchWithTX(ctx context.Context, tx Transaction, relations []BorrowDocumentRelation) error {
	// 实现步骤:
	// 1. 在事务中批量创建借阅文档关系记录
	if len(relations) == 0 {
		return nil
	}
	if err := tx.GetTX().WithContext(ctx).Create(&relations).Error; err != nil {
		logc.Error(ctx, "Failed to create borrow document relations in batch with transaction", err)
		return err
	}
	return nil
}

// GetByBorrowRecordID 根据借阅记录ID获取文档关系列表
// 功能: 查询指定借阅记录的所有文档关系
// 参数:
//
//	ctx: 上下文对象
//	borrowRecordID: 借阅记录ID
//
// 返回值:
//
//	[]BorrowDocumentRelation: 文档关系列表
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *BorrowDocumentRelationClient) GetByBorrowRecordID(ctx context.Context, borrowRecordID string) ([]BorrowDocumentRelation, error) {
	// 实现步骤:
	// 1. 根据借阅记录ID查询文档关系列表
	var relations []BorrowDocumentRelation
	if err := c.db.WithContext(ctx).Where("borrow_record_id = ?", borrowRecordID).Find(&relations).Error; err != nil {
		logc.Error(ctx, "Failed to get borrow document relations by borrow record ID", err)
		return nil, err
	}
	return relations, nil
}

// GetByDocumentID 根据文档ID获取借阅关系列表
// 功能: 查询指定文档的所有借阅关系
// 参数:
//
//	ctx: 上下文对象
//	documentID: 文档ID
//
// 返回值:
//
//	[]BorrowDocumentRelation: 借阅关系列表
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *BorrowDocumentRelationClient) GetByDocumentID(ctx context.Context, documentID string) ([]BorrowDocumentRelation, error) {
	// 实现步骤:
	// 1. 根据文档ID查询借阅关系列表
	var relations []BorrowDocumentRelation
	if err := c.db.WithContext(ctx).Where("document_id = ?", documentID).Find(&relations).Error; err != nil {
		logc.Error(ctx, "Failed to get borrow document relations by document ID", err)
		return nil, err
	}
	return relations, nil
}

// UpdateStatus 更新文档借阅状态
// 功能: 更新指定文档的借阅状态
// 参数:
//
//	ctx: 上下文对象
//	borrowRecordID: 借阅记录ID
//	documentID: 文档ID
//	status: 新的借阅状态
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *BorrowDocumentRelationClient) UpdateStatus(ctx context.Context, borrowRecordID, documentID string, status int32) error {
	// 实现步骤:
	// 1. 更新指定文档的借阅状态
	if err := c.db.WithContext(ctx).Model(&BorrowDocumentRelation{}).
		Where("borrow_record_id = ? AND document_id = ?", borrowRecordID, documentID).
		Update("borrow_status", status).Error; err != nil {
		logc.Error(ctx, "Failed to update borrow document relation status", err)
		return err
	}
	return nil
}

// UpdateStatusWithTX 使用事务更新借阅文档关系状态
func (c *BorrowDocumentRelationClient) UpdateStatusWithTX(ctx context.Context, tx Transaction, borrowRecordID, documentID string, status int32) error {
	return tx.GetTX().WithContext(ctx).Model(&BorrowDocumentRelation{}).
		Where("borrow_record_id = ? AND document_id = ?", borrowRecordID, documentID).
		Updates(map[string]interface{}{
			"borrow_status": status,
			"updated_at":    time.Now(),
		}).Error
}

// UpdateWithTX 使用事务更新借阅文档关系完整信息
func (c *BorrowDocumentRelationClient) UpdateWithTX(ctx context.Context, tx Transaction, relation *BorrowDocumentRelation) error {
	return tx.GetTX().WithContext(ctx).Save(relation).Error
}

// DeleteByBorrowRecordID 根据借阅记录ID删除文档关系
// 功能: 删除指定借阅记录的所有文档关系
// 参数:
//
//	ctx: 上下文对象
//	borrowRecordID: 借阅记录ID
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *BorrowDocumentRelationClient) DeleteByBorrowRecordID(ctx context.Context, borrowRecordID string) error {
	// 实现步骤:
	// 1. 删除指定借阅记录的所有文档关系
	if err := c.db.WithContext(ctx).Where("borrow_record_id = ?", borrowRecordID).Delete(&BorrowDocumentRelation{}).Error; err != nil {
		logc.Error(ctx, "Failed to delete borrow document relations by borrow record ID", err)
		return err
	}
	return nil
}