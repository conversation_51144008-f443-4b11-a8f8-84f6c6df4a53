package referenceimpl

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/domain/value"
	"docvault/rpc/internal/infrastructure/adapter/addons"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"encoding/json"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/datatypes"
)

type DocumentLibraryReferenceImpl struct {
	db          *mapper.DocvaultDB
	idGenerator addons.IDGeneratorAddons
}

func NewDocumentLibraryReferenceImpl(
	docvaultDB *mapper.DocvaultDB,
	idGenerator addons.IDGeneratorAddons,
) aggregate.DocumentLibraryReference {
	return &DocumentLibraryReferenceImpl{
		db:          docvaultDB,
		idGenerator: idGenerator,
	}
}

func (d *DocumentLibraryReferenceImpl) CreateDistributeRecord(ctx context.Context, distributeRecordInfo *entity.DistributeRecordInfo) error {
	// 创建 Clients
	recordClient := mapper.NewDistributeRecordClient(d.db)
	fileClient := mapper.NewDistributeRecordFileClient(d.db)
	permissionClient := mapper.NewDistributeRecordPermissionClient(d.db)

	// 开始事务
	tx, err := mapper.NewTransaction(d.db.GetDB()).Begin(ctx)
	if err != nil {
		return err
	}

	// 使用 defer 和 AutoCommit 来自动处理事务提交或回滚
	defer tx.AutoCommit(&err)

	// 序列化 ApprovalInfo
	approvalInfoBytes, err := json.Marshal(distributeRecordInfo.ApprovalInfo)
	if err != nil {
		return err
	}

	// 创建 DistributeRecord
	record := &mapper.DistributeRecord{
		ID:                 distributeRecordInfo.ID,
		WorkflowID:         distributeRecordInfo.WorkflowID,
		Applicant:          distributeRecordInfo.Applicant,
		ApplyDate:          distributeRecordInfo.ApplyDate,
		DistributeType:     distributeRecordInfo.DistributeType,
		FileType:           distributeRecordInfo.FileType,
		FileCategory:       distributeRecordInfo.FileCategory,
		TypeDictNodeId:     distributeRecordInfo.TypeDictNodeId,
		Reason:             distributeRecordInfo.Reason,
		OtherReason:        distributeRecordInfo.OtherReason,
		WishDistributeDate: distributeRecordInfo.WishDistributeDate,
		DistributeCount:    distributeRecordInfo.DistributeCount,
		SignForCount:       distributeRecordInfo.SignForCount,
		DisposeCount:       distributeRecordInfo.DisposeCount,
		Status:             distributeRecordInfo.Status,
		ApprovalInfo:       datatypes.JSON(approvalInfoBytes),
	}

	// 插入 DistributeRecord
	if err = recordClient.CreateWithTx(ctx, tx, record); err != nil {
		return err
	}

	// 创建 DistributeRecordFile 和 DistributeRecordPermission
	for _, fileList := range distributeRecordInfo.DistributeList {
		fileRecord := &mapper.DistributeRecordFile{
			ID:       d.idGenerator.GenerateIDString(),
			RecordID: record.ID,
			FileID:   fileList.FileID,
			FileName: fileList.FileName,
			Number:   fileList.Number,
			Version:  fileList.Version,
		}

		// 插入 DistributeRecordFile
		if err = fileClient.CreateWithTx(ctx, tx, fileRecord); err != nil {
			return err
		}

		// 创建 DistributeRecordPermission
		for _, permission := range fileList.Permissions {
			for _, receivedBy := range permission.ReceivedBy {
				permissionRecord := &mapper.DistributeRecordPermission{
					ID:             d.idGenerator.GenerateIDString(),
					FileRecordID:   fileRecord.ID,
					FileForm:       permission.FileForm,
					FilePermission: permission.FilePermission,
					Recipient:      permission.Recipient,
					UserID:         receivedBy.UserId,
					UserName:       receivedBy.UserName,
					SignForStatus:  receivedBy.SignForStatus,
					DisposeStatus:  receivedBy.DisposeStatus,
				}

				// 插入 DistributeRecordPermission
				if err = permissionClient.CreateWithTx(ctx, tx, permissionRecord); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

// GetUserIdsByFileIdAndFileFromAndFilePermission 根据文件id、文件类型（内外部文件）、文件权限类型（查询、一次下载），查询用户id
// 参数:
//   - ctx: 上下文
//   - fileId: 文件ID
//   - fileFrom: 文件类型，1内部文件 | 2外部文件
//   - filePermission: 文件权限，1查阅 | 2查阅/下载 | 3一次下载
//
// 返回:
//   - *value.DocPermissionUsers: 包含三类用户的权限信息
//   - error: 错误信息，成功时返回nil
func (d *DocumentLibraryReferenceImpl) GetUserIdsByFileIdAndFileFromAndFilePermission(ctx context.Context, fileId string, fileFrom, filePermission int32) (*value.DocPermissionUsers, error) {
	// 初始化结果结构
	result := &value.DocPermissionUsers{
		WaitForApprovalUsers: []string{},
		NotRecycledUsers:     []string{},
		RecycleFlowUsers:     []string{},
	}

	// 通过mapper层查询详细的用户权限状态信息
	permissionClient := mapper.NewDistributeRecordPermissionClient(d.db)
	userPermissions, err := permissionClient.GetDetailedUserPermissions(ctx, fileId, fileFrom, filePermission)
	if err != nil {
		return nil, fmt.Errorf("查询用户权限详细信息失败: %v", err)
	}

	// 使用map去重，避免重复用户
	// 回收审批中
	waitForApprovalMap := make(map[string]bool)
	// 未回收
	notRecycledMap := make(map[string]bool)
	// 已回收
	recycleFlowMap := make(map[string]bool)

	for _, perm := range userPermissions {
		switch perm.DistributeStatus {
		case 2: // 待审批
			waitForApprovalMap[perm.UserID] = true
		case 3: // 已审批 - 根据处置状态进一步分类
			switch perm.DisposeStatus {
			case 1: // 未回收
				notRecycledMap[perm.UserID] = true
			case 2: // 回收审批中
				recycleFlowMap[perm.UserID] = true
			}
		}
	}

	// 查询回收记录中的用户权限信息
	recycleUsers, err := d.getRecycleDocPermissionUsers(ctx, fileId, fileFrom, filePermission)
	if err != nil {
		return nil, fmt.Errorf("查询回收记录用户权限失败: %v", err)
	}

	// 处理回收记录用户
	for _, user := range recycleUsers {
		switch user.RecycleStatus {
		case 2: // 回收审批中
			recycleFlowMap[user.UserID] = true
		}
	}

	// 转换map为slice
	for userID := range waitForApprovalMap {
		result.WaitForApprovalUsers = append(result.WaitForApprovalUsers, userID)
	}
	for userID := range notRecycledMap {
		result.NotRecycledUsers = append(result.NotRecycledUsers, userID)
	}
	for userID := range recycleFlowMap {
		result.RecycleFlowUsers = append(result.RecycleFlowUsers, userID)
	}

	return result, nil
}

// 根据发放记录ID修改状态
func (d *DocumentLibraryReferenceImpl) AlterDistributeStatus(ctx context.Context, id string, status int32) error {
	// 根据发放记录ID修改状态
	recordClient := mapper.NewDistributeRecordClient(d.db)
	return recordClient.UpdateStatusByWorkflowId(ctx, id, status)
}

// 根据workflowId修改status
func (d *DocumentLibraryReferenceImpl) AlterDistributeStatusByWorkflowId(ctx context.Context, workflowId string, status int32) error {
	// 根据workflowId修改status
	recordClient := mapper.NewDistributeRecordClient(d.db)
	return recordClient.UpdateStatusByWorkflowId(ctx, workflowId, status)
}

// SaveRecycleInfo 保存回收信息并更新发放权限表中的处置状态
// 业务流程：
//  1. 创建回收记录、回收文件记录和回收权限记录
//  2. 更新发放记录权限表中的处置状态为已回收（状态值：3）
//  3. 在事务中确保数据一致性
//
// 参数:
//   - ctx: 上下文，用于传递请求范围的值、取消信号和截止时间
//   - recycleApprovalRequest: 回收审批信息
//
// 返回:
//   - error: 保存失败时返回错误，成功时返回nil
//
// 注意事项:
//   - 使用数据库事务确保回收记录创建和权限状态更新的一致性
//   - 处置状态更新为已回收（3）会影响用户的文件访问权限
func (d *DocumentLibraryReferenceImpl) SaveRecycleInfo(ctx context.Context, recycleApprovalRequest *entity.RecycleApprovalInfo) error {
	// 1. 构建回收记录数据
	record, fileRecords, permissions, err := d.buildRecycleRecords(ctx, recycleApprovalRequest)
	if err != nil {
		return err
	}

	// 2. 在事务中执行保存和更新操作
	return d.executeRecycleSaveTransaction(ctx, recycleApprovalRequest, record, fileRecords, permissions)
}

// SaveDisposalInfo 保存处置信息并更新发放权限表中的处置状态
// 业务流程：
//  1. 创建处置记录、处置文件记录和处置权限记录
//  2. 更新发放记录权限表中的处置状态为已处置（状态值：5）
//  3. 在事务中确保数据一致性
//
// 参数:
//   - ctx: 上下文，用于传递请求范围的值、取消信号和截止时间
//   - disposalApprovalRequest: 处置审批信息
//
// 返回:
//   - error: 保存失败时返回错误，成功时返回nil
//
// 注意事项:
//   - 使用数据库事务确保处置记录创建和权限状态更新的一致性
//   - 处置状态更新为已处置（5）会影响用户的文件访问权限
func (d *DocumentLibraryReferenceImpl) SaveDisposalInfo(ctx context.Context, disposalApprovalRequest *entity.DisposalApprovalInfo) error {
	// 1. 构建处置记录数据
	record, fileRecords, permissions, err := d.buildDisposalRecords(ctx, disposalApprovalRequest)
	if err != nil {
		return err
	}

	// 2. 在事务中执行保存和更新操作
	return d.executeDisposalSaveTransaction(ctx, disposalApprovalRequest, record, fileRecords, permissions)
}

// SaveDistributeApprovalWorkflow 保存发放审批流程信息
// 根据 workflowID 更新发放记录的状态和审批信息
// 参数:
//   - ctx: 上下文
//   - req: 发放审批请求，包含工作流ID、状态和审批信息
//
// 返回:
//   - error: 错误信息，成功时返回nil
func (d *DocumentLibraryReferenceImpl) SaveDistributeApprovalWorkflow(ctx context.Context, req *value.DistributeApprovalRequest) error {
	// 创建分发记录客户端
	recordClient := mapper.NewDistributeRecordClient(d.db)

	// 根据 workflowID 获取现有的分发记录
	record, err := recordClient.GetByWorkflowID(ctx, req.WorkflowID)
	if err != nil {
		return err
	}

	// 检查记录是否存在
	if record == nil {
		return fmt.Errorf("未找到工作流ID为 %s 的分发记录", req.WorkflowID)
	}

	// 序列化审批信息为JSON
	approvalInfoJSON, err := json.Marshal(req.ApprovalInfo)
	if err != nil {
		return fmt.Errorf("序列化审批信息失败: %v", err)
	}

	// 更新记录的状态和审批信息
	record.Status = req.Status
	record.ApprovalInfo = datatypes.JSON(approvalInfoJSON)

	// 保存更新后的记录
	if err := recordClient.Update(ctx, record); err != nil {
		return fmt.Errorf("更新分发记录失败: %v", err)
	}

	return nil
}

func (d *DocumentLibraryReferenceImpl) GetDistributeInfos(ctx context.Context, req *value.GetDistributeListRequest) (int, []*entity.GetDistributeListInfo, error) {
	// 创建分发记录客户端
	recordClient := mapper.NewDistributeRecordClient(d.db)
	permissionClient := mapper.NewDistributeRecordPermissionClient(d.db)

	// 构建查询请求参数
	getDistributesReq := &mapper.GetDistributesRequest{
		FileNumber:     req.FileNumber,
		FileName:       req.FileName,
		FileType:       req.FileType,
		FileCategory:   req.FileCategory,
		DistributeType: req.DistributeType,
		Status:         req.Status,
		Applicant:      req.Applicant,
		Page:           int(req.Page),
		PageSize:       int(req.PageSize),
		NoPage:         req.NoPage,
	}

	// 查询分发记录列表和总数
	records, total, err := recordClient.GetDistributes(ctx, getDistributesReq)
	if err != nil {
		return 0, nil, err
	}

	if len(records) == 0 {
		return int(total), []*entity.GetDistributeListInfo{}, nil
	}

	// 批量获取用户状态信息，避免N+1查询问题
	var recordIDs []string
	for _, record := range records {
		recordIDs = append(recordIDs, record.ID)
	}

	// 批量查询所有记录的用户状态原始数据
	userStatuses, err := permissionClient.GetUserStatusesByRecordIDs(ctx, recordIDs)
	if err != nil {
		return 0, nil, err
	}

	// 在impl层进行用户状态分类
	receivedMap, notReceivedMap, recycleMap := d.classifyUsersByStatus(userStatuses)

	// 确保每个记录ID都有对应的映射
	d.ensureRecordMappings(recordIDs, receivedMap, notReceivedMap, recycleMap)

	// 转换为返回格式
	var result []*entity.GetDistributeListInfo
	for _, record := range records {
		// 反序列化审批信息
		var approvalInfo entity.ApprovalInfo
		if len(record.ApprovalInfo) > 0 {
			if err := json.Unmarshal(record.ApprovalInfo, &approvalInfo); err != nil {
				// 如果反序列化失败，使用空的审批信息
				approvalInfo = entity.ApprovalInfo{}
			}
		}

		// 从批量查询结果中获取用户状态信息
		received := receivedMap[record.ID]
		notReceived := notReceivedMap[record.ID]
		recycle := recycleMap[record.ID]

		// 构建分发信息
		distributeInfo := &entity.GetDistributeListInfo{
			ID:                 record.ID,
			Applicant:          record.Applicant,
			ApplyDate:          record.ApplyDate,
			DistributeType:     int(record.DistributeType),
			FileType:           int(record.FileType),
			FileCategory:       record.FileCategory,
			WishDistributeDate: record.WishDistributeDate,
			Reason:             record.Reason,
			OtherReason:        record.OtherReason,
			Status:             record.Status,
			WorkflowID:         record.WorkflowID,
			ApprovalInfo:       approvalInfo,
			Received:           d.convertDistributeUsers(received),
			NotReceived:        d.convertDistributeUsers(notReceived),
			Recycle:            d.convertDistributeUsers(recycle),
		}

		result = append(result, distributeInfo)
	}

	return int(total), result, nil
}

// getRecycleDocPermissionUsers 获取回收记录中的用户权限信息
func (d *DocumentLibraryReferenceImpl) getRecycleDocPermissionUsers(ctx context.Context, fileId string, fileFrom, filePermission int32) ([]value.RecycleDocPermissionUser, error) {
	var result []value.RecycleDocPermissionUser

	// 使用mapper查询回收权限用户信息
	permissionClient := mapper.NewRecycleRecordPermissionClient(d.db)
	permissions, err := permissionClient.GetRecycleDocPermissionUsers(ctx, fileId, fileFrom, filePermission)
	if err != nil {
		return nil, err
	}

	// 转换为返回格式
	for _, perm := range permissions {
		result = append(result, value.RecycleDocPermissionUser{
			UserID:        perm.UserID,
			RecycleStatus: perm.RecycleStatus,
		})
	}

	return result, nil
}

// classifyUsersByStatus 根据签收状态和处置状态分类用户
// 参数:
//   - userStatuses: 用户状态列表
//
// 返回:
//   - receivedMap: 按记录ID分组的已签收用户映射
//   - notReceivedMap: 按记录ID分组的未签收用户映射
//   - recycleMap: 按记录ID分组的已回收用户映射
func (d *DocumentLibraryReferenceImpl) classifyUsersByStatus(userStatuses []mapper.UserStatusInfo) (
	receivedMap, notReceivedMap, recycleMap map[string][]mapper.DistributeUser) {

	// 初始化映射
	receivedMap = make(map[string][]mapper.DistributeUser)
	notReceivedMap = make(map[string][]mapper.DistributeUser)
	recycleMap = make(map[string][]mapper.DistributeUser)

	// 按记录ID和状态分组用户
	for _, status := range userStatuses {
		user := mapper.DistributeUser{
			UserID:   status.UserID,
			FileForm: int(status.FileForm),
		}

		// 根据状态分类用户
		switch {
		case status.DisposeStatus >= 3: // 已回收
			recycleMap[status.RecordID] = append(recycleMap[status.RecordID], user)
		}

		switch {
		case status.SignForStatus == 2: // 已签收
			receivedMap[status.RecordID] = append(receivedMap[status.RecordID], user)
		case status.SignForStatus == 1: // 未签收
			notReceivedMap[status.RecordID] = append(notReceivedMap[status.RecordID], user)
		}

	}

	return receivedMap, notReceivedMap, recycleMap
}

// ensureRecordMappings 确保每个记录ID都有对应的映射（即使为空）
// 参数:
//   - recordIDs: 记录ID列表
//   - receivedMap: 已签收用户映射
//   - notReceivedMap: 未签收用户映射
//   - recycleMap: 已回收用户映射
func (d *DocumentLibraryReferenceImpl) ensureRecordMappings(recordIDs []string,
	receivedMap, notReceivedMap, recycleMap map[string][]mapper.DistributeUser) {

	for _, recordID := range recordIDs {
		if _, exists := receivedMap[recordID]; !exists {
			receivedMap[recordID] = []mapper.DistributeUser{}
		}
		if _, exists := notReceivedMap[recordID]; !exists {
			notReceivedMap[recordID] = []mapper.DistributeUser{}
		}
		if _, exists := recycleMap[recordID]; !exists {
			recycleMap[recordID] = []mapper.DistributeUser{}
		}
	}
}

// convertDistributeUsers 转换 mapper.DistributeUser 到 entity.DistributeUser
// 参数:
//   - mapperUsers: mapper层的用户列表
//
// 返回:
//   - []entity.DistributeUser: entity层的用户列表
func (d *DocumentLibraryReferenceImpl) convertDistributeUsers(mapperUsers []mapper.DistributeUser) []entity.DistributeUser {
	var entityUsers []entity.DistributeUser
	for _, mapperUser := range mapperUsers {
		entityUser := entity.DistributeUser{
			UserID:   mapperUser.UserID,
			FileForm: mapperUser.FileForm,
		}
		entityUsers = append(entityUsers, entityUser)
	}
	return entityUsers
}

// GetDistributeInventoryByID 根据发放列表id获取发放清单信息
// 参数:
//   - ctx: 上下文
//   - id: 发放列表ID（即发放记录ID）
//
// 返回:
//   - []*entity.DistributeInventory: 发放清单信息列表
//   - error: 错误信息，成功时返回nil
func (d *DocumentLibraryReferenceImpl) GetDistributeInventoryByID(ctx context.Context, id string) ([]*entity.DistributeInventory, error) {
	// 创建mapper客户端
	fileClient := mapper.NewDistributeRecordFileClient(d.db)
	permissionClient := mapper.NewDistributeRecordPermissionClient(d.db)

	// 1. 根据发放记录ID获取文件列表
	files, err := fileClient.GetInventoryByRecordID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("获取发放文件列表失败: %v", err)
	}

	if len(files) == 0 {
		return []*entity.DistributeInventory{}, nil
	}

	// 2. 收集所有文件记录ID
	var fileRecordIDs []string
	for _, file := range files {
		fileRecordIDs = append(fileRecordIDs, file.ID)
	}

	// 3. 批量获取权限信息
	permissions, err := permissionClient.GetPermissionsByFileRecordIDs(ctx, fileRecordIDs)
	if err != nil {
		return nil, fmt.Errorf("获取权限信息失败: %v", err)
	}

	// 4. 构建发放清单信息
	inventories := d.buildDistributeInventories(files, permissions)

	return inventories, nil
}

// buildDistributeInventories 构建发放清单信息
// 参数:
//   - files: 文件列表
//   - permissions: 权限信息列表
//
// 返回:
//   - []*entity.DistributeInventory: 构建好的发放清单信息列表
func (d *DocumentLibraryReferenceImpl) buildDistributeInventories(files []*mapper.DistributeRecordFile, permissions []*mapper.DistributeRecordPermission) []*entity.DistributeInventory {
	// 按文件记录ID分组权限信息
	permissionMap := d.groupPermissionsByFileRecordID(permissions)

	// 构建发放清单信息
	var inventories []*entity.DistributeInventory
	for _, file := range files {
		inventory := d.buildSingleInventory(file, permissionMap[file.ID])
		inventories = append(inventories, inventory)
	}

	return inventories
}

// groupPermissionsByFileRecordID 按文件记录ID分组权限信息
// 参数:
//   - permissions: 权限信息列表
//
// 返回:
//   - map[string][]*mapper.DistributeRecordPermission: 按文件记录ID分组的权限信息映射
func (d *DocumentLibraryReferenceImpl) groupPermissionsByFileRecordID(permissions []*mapper.DistributeRecordPermission) map[string][]*mapper.DistributeRecordPermission {
	permissionMap := make(map[string][]*mapper.DistributeRecordPermission)
	for _, permission := range permissions {
		permissionMap[permission.FileRecordID] = append(permissionMap[permission.FileRecordID], permission)
	}
	return permissionMap
}

// buildSingleInventory 构建单个发放清单项
// 参数:
//   - file: 文件信息
//   - filePermissions: 该文件的权限信息列表
//
// 返回:
//   - *entity.DistributeInventory: 构建好的发放清单项
func (d *DocumentLibraryReferenceImpl) buildSingleInventory(file *mapper.DistributeRecordFile, filePermissions []*mapper.DistributeRecordPermission) *entity.DistributeInventory {
	inventory := &entity.DistributeInventory{
		ID:       file.ID,
		FileId:   file.FileID,
		FileName: file.FileName,
		Number:   file.Number,
		Version:  file.Version,
	}

	// 如果有权限信息，则构建权限列表
	if len(filePermissions) > 0 {
		inventory.Permissions = d.buildPermissionResponses(filePermissions)
	}

	return inventory
}

// buildPermissionResponses 构建权限响应列表
// 参数:
//   - filePermissions: 文件权限信息列表
//
// 返回:
//   - []entity.PermissionResp: 权限响应列表
func (d *DocumentLibraryReferenceImpl) buildPermissionResponses(filePermissions []*mapper.DistributeRecordPermission) []entity.PermissionResp {
	// 按文件形式和权限类型分组
	permissionGroupMap := d.groupPermissionsByFormAndType(filePermissions)

	// 转换为权限列表
	var permissions []entity.PermissionResp
	for _, permResp := range permissionGroupMap {
		permissions = append(permissions, *permResp)
	}

	return permissions
}

// groupPermissionsByFormAndType 按文件形式和权限类型分组权限信息
// 参数:
//   - filePermissions: 文件权限信息列表
//
// 返回:
//   - map[string]*entity.PermissionResp: 按权限组合键分组的权限响应映射
func (d *DocumentLibraryReferenceImpl) groupPermissionsByFormAndType(filePermissions []*mapper.DistributeRecordPermission) map[string]*entity.PermissionResp {
	permissionGroupMap := make(map[string]*entity.PermissionResp)

	for _, perm := range filePermissions {
		// 创建权限组合键：文件形式_权限类型
		key := fmt.Sprintf("%d_%d", perm.FileForm, perm.FilePermission)

		if permissionGroupMap[key] == nil {
			permissionGroupMap[key] = &entity.PermissionResp{
				FileForm:       perm.FileForm,
				FilePermission: perm.FilePermission,
				ReceivedBy:     []entity.Received{},
			}
		}

		// 添加接收人信息
		received := entity.Received{
			UserID:   perm.UserID,
			Nickname: perm.UserName,
			Status:   int(perm.SignForStatus), // 使用签收状态作为状态
		}
		permissionGroupMap[key].ReceivedBy = append(permissionGroupMap[key].ReceivedBy, received)
	}

	return permissionGroupMap
}

// UpdateDistributeInventoryByID 根据发放列表ID更新发放清单信息
// 业务流程：
//  1. 根据ID获取现有发放记录，验证记录存在性
//  2. 在事务中删除旧的发放清单数据（文件记录和权限记录）
//  3. 更新发放记录的基本信息
//  4. 保存新的发放清单数据（文件记录和权限记录）
//
// 参数:
//   - ctx: 上下文，用于传递请求范围的值、取消信号和截止时间
//   - req: 发放记录信息，包含要更新的所有数据
//
// 返回:
//   - error: 更新失败时返回错误，成功时返回nil
//
// 注意事项:
//   - 使用数据库事务确保数据一致性
//   - 删除操作会级联删除相关的权限记录
//   - 更新过程中如果发生错误会自动回滚
func (d *DocumentLibraryReferenceImpl) UpdateDistributeInventoryByID(ctx context.Context, req *entity.DistributeRecordInfo) error {
	// 创建数据访问客户端
	recordClient := mapper.NewDistributeRecordClient(d.db)
	fileClient := mapper.NewDistributeRecordFileClient(d.db)
	permissionClient := mapper.NewDistributeRecordPermissionClient(d.db)

	// 1. 验证发放记录是否存在
	if err := d.validateDistributeRecordExists(ctx, recordClient, req.ID); err != nil {
		return err
	}

	// 2. 开始数据库事务
	tx, err := mapper.NewTransaction(d.db.GetDB()).Begin(ctx)
	if err != nil {
		return fmt.Errorf("开始事务失败: %v", err)
	}

	// 使用 defer 和 AutoCommit 来自动处理事务提交或回滚
	defer tx.AutoCommit(&err)

	// 3. 删除旧的发放清单数据
	if err = d.deleteOldInventoryData(ctx, tx, fileClient, permissionClient, req.ID); err != nil {
		return err
	}

	// 4. 更新发放记录的基本信息
	if err = d.updateDistributeRecord(ctx, tx, recordClient, req); err != nil {
		return err
	}

	// 5. 保存新的发放清单数据
	if err = d.createNewInventoryData(ctx, tx, fileClient, permissionClient, req); err != nil {
		return err
	}

	return nil
}

// validateDistributeRecordExists 验证发放记录是否存在
// 参数:
//   - ctx: 上下文
//   - recordClient: 发放记录数据访问客户端
//   - recordID: 发放记录ID
//
// 返回:
//   - error: 记录不存在或查询失败时返回错误，成功时返回nil
func (d *DocumentLibraryReferenceImpl) validateDistributeRecordExists(ctx context.Context, recordClient *mapper.DistributeRecordClient, recordID string) error {
	existingRecord, err := recordClient.GetByID(ctx, recordID)
	if err != nil {
		return fmt.Errorf("获取发放记录失败: %v", err)
	}
	if existingRecord == nil {
		return fmt.Errorf("未找到ID为 %s 的发放记录", recordID)
	}
	return nil
}

// deleteOldInventoryData 删除旧的发放清单数据
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - fileClient: 文件记录数据访问客户端
//   - permissionClient: 权限记录数据访问客户端
//   - recordID: 发放记录ID
//
// 返回:
//   - error: 删除失败时返回错误，成功时返回nil
func (d *DocumentLibraryReferenceImpl) deleteOldInventoryData(ctx context.Context, tx mapper.Transaction, fileClient *mapper.DistributeRecordFileClient, permissionClient *mapper.DistributeRecordPermissionClient, recordID string) error {
	// 获取所有相关的文件记录
	oldFiles, err := fileClient.GetByRecordID(ctx, recordID)
	if err != nil {
		return fmt.Errorf("获取旧文件记录失败: %v", err)
	}

	// 提取文件记录ID列表
	fileRecordIDs := make([]string, len(oldFiles))
	for i, file := range oldFiles {
		fileRecordIDs[i] = file.ID
	}

	// 删除所有相关的权限记录
	if err = permissionClient.DeleteByFileRecordIDsWithTx(ctx, tx, fileRecordIDs); err != nil {
		return fmt.Errorf("删除旧权限记录失败: %v", err)
	}

	// 删除所有文件记录
	if err = fileClient.DeleteByRecordIDWithTx(ctx, tx, recordID); err != nil {
		return fmt.Errorf("删除旧文件记录失败: %v", err)
	}

	return nil
}

// updateDistributeRecord 更新发放记录的基本信息
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - recordClient: 发放记录数据访问客户端
//   - req: 发放记录信息
//
// 返回:
//   - error: 更新失败时返回错误，成功时返回nil
func (d *DocumentLibraryReferenceImpl) updateDistributeRecord(ctx context.Context, tx mapper.Transaction, recordClient *mapper.DistributeRecordClient, req *entity.DistributeRecordInfo) error {
	// 序列化审批信息
	approvalInfoBytes, err := json.Marshal(req.ApprovalInfo)
	if err != nil {
		return fmt.Errorf("序列化审批信息失败: %v", err)
	}

	// 构建更新的发放记录
	updatedRecord := &mapper.DistributeRecord{
		ID:                 req.ID,
		WorkflowID:         req.WorkflowID,
		Applicant:          req.Applicant,
		ApplyDate:          req.ApplyDate,
		DistributeType:     req.DistributeType,
		FileType:           req.FileType,
		FileCategory:       req.FileCategory,
		TypeDictNodeId:     req.TypeDictNodeId,
		Reason:             req.Reason,
		OtherReason:        req.OtherReason,
		WishDistributeDate: req.WishDistributeDate,
		DistributeCount:    req.DistributeCount,
		SignForCount:       req.SignForCount,
		DisposeCount:       req.DisposeCount,
		Status:             req.Status,
		ApprovalInfo:       datatypes.JSON(approvalInfoBytes),
	}

	// 使用事务更新发放记录
	if err = recordClient.UpdateWithTx(ctx, tx, updatedRecord); err != nil {
		return fmt.Errorf("更新发放记录失败: %v", err)
	}

	return nil
}

// createNewInventoryData 创建新的发放清单数据
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - fileClient: 文件记录数据访问客户端
//   - permissionClient: 权限记录数据访问客户端
//   - req: 发放记录信息
//
// 返回:
//   - error: 创建失败时返回错误，成功时返回nil
func (d *DocumentLibraryReferenceImpl) createNewInventoryData(ctx context.Context, tx mapper.Transaction, fileClient *mapper.DistributeRecordFileClient, permissionClient *mapper.DistributeRecordPermissionClient, req *entity.DistributeRecordInfo) error {
	// 遍历发放清单，创建文件记录和权限记录
	for _, fileList := range req.DistributeList {
		// 创建文件记录
		fileRecord, err := d.buildFileRecord(req.ID, &fileList)
		if err != nil {
			return fmt.Errorf("构建文件记录失败: %v", err)
		}

		// 插入文件记录
		if err = fileClient.CreateWithTx(ctx, tx, fileRecord); err != nil {
			return fmt.Errorf("创建文件记录失败: %v", err)
		}

		// 创建权限记录
		if err = d.createPermissionRecords(ctx, tx, permissionClient, fileRecord.ID, fileList.Permissions); err != nil {
			return fmt.Errorf("创建权限记录失败: %v", err)
		}
	}

	return nil
}

// buildFileRecord 构建文件记录
// 参数:
//   - recordID: 发放记录ID
//   - fileList: 文件清单信息
//
// 返回:
//   - *mapper.DistributeRecordFile: 构建好的文件记录
//   - error: 构建失败时返回错误，成功时返回nil
func (d *DocumentLibraryReferenceImpl) buildFileRecord(recordID string, fileList *entity.DistributeList) (*mapper.DistributeRecordFile, error) {
	return &mapper.DistributeRecordFile{
		ID:       d.idGenerator.GenerateIDString(),
		RecordID: recordID,
		FileID:   fileList.FileID,
		FileName: fileList.FileName,
		Number:   fileList.Number,
		Version:  fileList.Version,
	}, nil
}

// createPermissionRecords 创建权限记录
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - permissionClient: 权限记录数据访问客户端
//   - fileRecordID: 文件记录ID
//   - permissions: 权限信息列表
//
// 返回:
//   - error: 创建失败时返回错误，成功时返回nil
func (d *DocumentLibraryReferenceImpl) createPermissionRecords(ctx context.Context, tx mapper.Transaction, permissionClient *mapper.DistributeRecordPermissionClient, fileRecordID string, permissions []entity.Permission) error {
	for _, permission := range permissions {
		for _, receivedBy := range permission.ReceivedBy {
			permissionRecord := &mapper.DistributeRecordPermission{
				ID:             d.idGenerator.GenerateIDString(),
				FileRecordID:   fileRecordID,
				FileForm:       permission.FileForm,
				FilePermission: permission.FilePermission,
				Recipient:      permission.Recipient,
				UserID:         receivedBy.UserId,
				UserName:       receivedBy.UserName,
				SignForStatus:  receivedBy.SignForStatus,
				DisposeStatus:  receivedBy.DisposeStatus,
			}

			// 插入权限记录
			if err := permissionClient.CreateWithTx(ctx, tx, permissionRecord); err != nil {
				return err
			}
		}
	}

	return nil
}

// DeleteDistributeRecordByID 根据发放列表id删除发放记录
// 业务流程：
//  1. 验证发放记录是否存在
//  2. 在事务中删除相关数据：
//     - 删除发放权限数据
//     - 删除发放清单数据
//     - 删除发放记录
//
// 参数:
//   - ctx: 上下文
//   - id: 发放列表ID
//
// 返回:
//   - error: 删除失败时返回错误，成功时返回nil
//
// 注意事项:
//   - 使用数据库事务确保数据一致性
//   - 删除操作按照外键依赖关系的逆序进行
//   - 删除过程中如果发生错误会自动回滚
func (d *DocumentLibraryReferenceImpl) DeleteDistributeRecordByID(ctx context.Context, id string) error {
	// 创建数据访问客户端
	recordClient := mapper.NewDistributeRecordClient(d.db)
	fileClient := mapper.NewDistributeRecordFileClient(d.db)
	permissionClient := mapper.NewDistributeRecordPermissionClient(d.db)

	// 1. 验证发放记录是否存在
	record, err := recordClient.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("查询发放记录失败: %v", err)
	}
	if record == nil {
		return fmt.Errorf("发放记录不存在，ID: %s", id)
	}

	// 2. 开始数据库事务
	tx, err := mapper.NewTransaction(d.db.GetDB()).Begin(ctx)
	if err != nil {
		return fmt.Errorf("开始事务失败: %v", err)
	}

	// 使用 defer 和 AutoCommit 来自动处理事务提交或回滚
	defer tx.AutoCommit(&err)

	// 3. 获取所有相关的文件记录
	files, err := fileClient.GetByRecordID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取发放文件记录失败: %v", err)
	}

	// 4. 删除权限数据（如果有文件记录）
	if len(files) > 0 {
		fileRecordIDs := make([]string, len(files))
		for i, file := range files {
			fileRecordIDs[i] = file.ID
		}

		// 删除所有相关的权限记录
		if err = permissionClient.DeleteByFileRecordIDsWithTx(ctx, tx, fileRecordIDs); err != nil {
			return fmt.Errorf("删除发放权限记录失败: %v", err)
		}
	}

	// 5. 删除发放清单数据
	if err = fileClient.DeleteByRecordIDWithTx(ctx, tx, id); err != nil {
		return fmt.Errorf("删除发放文件记录失败: %v", err)
	}

	// 6. 删除发放记录
	if err = recordClient.DeleteByIDWithTx(ctx, tx, id); err != nil {
		return fmt.Errorf("删除发放记录失败: %v", err)
	}

	return nil
}

// UpdateUserDisposalStatusByID 根据发放列表id和发放清单id和清单的权限更新用户处置状态
// 业务流程：
//  1. 根据发放记录ID和回收清单信息，查询对应的发放文件记录
//  2. 在数据库事务中更新发放记录权限表中用户的处置状态
//  3. 提交事务确保数据一致性
//
// 参数:
//   - ctx: 上下文，用于传递请求范围的值、取消信号和截止时间
//   - updateUserDisposalStatus: 更新用户处置状态的请求信息
//
// 返回:
//   - error: 更新失败时返回错误，成功时返回nil
//
// 注意事项:
//   - 处置状态更新会影响用户的文件访问权限
//   - 使用数据库事务确保数据一致性
//   - 更新过程中如果发生错误会自动回滚
func (d *DocumentLibraryReferenceImpl) UpdateUserDisposalStatusByID(ctx context.Context, updateUserDisposalStatus *value.UpdateUserDisposalStatus) error {
	// 创建数据访问客户端
	fileClient := mapper.NewDistributeRecordFileClient(d.db)
	distributePermissionClient := mapper.NewDistributeRecordPermissionClient(d.db)

	// 1. 根据发放记录ID获取所有发放文件记录
	distributeFiles, err := fileClient.GetByRecordID(ctx, updateUserDisposalStatus.DistributeID)
	if err != nil {
		logc.Error(ctx, "获取发放文件记录失败", err)
		return fmt.Errorf("获取发放文件记录失败: %v", err)
	}

	if len(distributeFiles) == 0 {
		logc.Error(ctx, "未找到发放文件记录")
		return fmt.Errorf("未找到发放文件记录")
	}

	// 2. 创建文件ID到文件记录的映射，便于后续查找
	fileRecordMap := make(map[string]*mapper.DistributeRecordFile)
	for _, file := range distributeFiles {
		fileRecordMap[file.ID] = file
	}

	// 3. 开始数据库事务
	tx, err := mapper.NewTransaction(d.db.GetDB()).Begin(ctx)
	if err != nil {
		return fmt.Errorf("开始事务失败: %v", err)
	}

	// 使用 defer 和 AutoCommit 来自动处理事务提交或回滚
	defer tx.AutoCommit(&err)

	// 4. 遍历回收清单，更新对应的权限状态
	for _, recycle := range updateUserDisposalStatus.Recycles {
		// 检查清单ID是否存在于发放文件记录中
		fileRecord, exists := fileRecordMap[recycle.InventoryID]
		if !exists {
			logc.Errorf(ctx, "清单ID %s 不存在于发放记录中", recycle.InventoryID)
			continue
		}

		// 5. 遍历权限信息，更新用户状态
		for _, permission := range recycle.Permissions {
			if len(permission.ReceivedBy) == 0 {
				continue
			}

			// 6. 使用 mapper 方法更新发放记录权限表中的处置状态
			err = distributePermissionClient.UpdateDisposeStatusByConditionsWithTx(
				ctx,
				tx,
				fileRecord.ID,
				int32(permission.FileForm),
				int32(permission.FilePermission),
				permission.ReceivedBy,
				updateUserDisposalStatus.DisposalStatus,
			)
			if err != nil {
				logc.Errorf(ctx, "更新发放记录权限表处置状态失败: %v", err)
				return fmt.Errorf("更新发放记录权限表处置状态失败: %v", err)
			}
		}
	}

	logc.Infof(ctx, "成功更新用户处置状态，发放记录ID: %s", updateUserDisposalStatus.DistributeID)
	return nil
}

// ========== 非导出方法（私有方法）==========

// buildRecycleRecords 构建回收记录相关的数据结构
// 参数:
//   - ctx: 上下文
//   - recycleApprovalRequest: 回收审批信息
//
// 返回:
//   - *mapper.RecycleRecord: 回收主记录
//   - []*mapper.RecycleRecordFile: 回收文件记录列表
//   - []*mapper.RecycleRecordPermission: 回收权限记录列表
//   - error: 构建失败时返回错误
func (d *DocumentLibraryReferenceImpl) buildRecycleRecords(_ context.Context, recycleApprovalRequest *entity.RecycleApprovalInfo) (*mapper.RecycleRecord, []*mapper.RecycleRecordFile, []*mapper.RecycleRecordPermission, error) {
	// 序列化审批信息为 datatypes.JSON
	approvalInfoJSON, err := json.Marshal(recycleApprovalRequest.ApprovalInfo)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("序列化审批信息失败: %v", err)
	}

	// 将int64时间戳转换为time.Time
	var recycleDate time.Time
	if recycleApprovalRequest.RecycleDate > 0 {
		recycleDate = time.UnixMilli(recycleApprovalRequest.RecycleDate)
	}

	// 创建主记录
	record := &mapper.RecycleRecord{
		ID:                 d.idGenerator.GenerateIDString(),
		DistributeRecordID: recycleApprovalRequest.DistributeID,
		RecycleBy:          recycleApprovalRequest.RecycleBy,
		RecycleDate:        recycleDate,
		RecycleReason:      recycleApprovalRequest.RecycleReason,
		OtherReason:        recycleApprovalRequest.OtherReason,
		WorkflowID:         recycleApprovalRequest.WorkflowID,
		ApprovalInfo:       approvalInfoJSON,
	}

	// 构建文件记录和权限记录
	fileRecords, permissions := d.buildRecycleFileAndPermissionRecords(record.ID, recycleApprovalRequest.RecycleList)

	return record, fileRecords, permissions, nil
}

// buildRecycleFileAndPermissionRecords 构建回收文件记录和权限记录
// 参数:
//   - recordID: 回收记录ID
//   - recycleList: 回收清单列表
//
// 返回:
//   - []*mapper.RecycleRecordFile: 回收文件记录列表
//   - []*mapper.RecycleRecordPermission: 回收权限记录列表
func (d *DocumentLibraryReferenceImpl) buildRecycleFileAndPermissionRecords(recordID string, recycleList []entity.RecycleList) ([]*mapper.RecycleRecordFile, []*mapper.RecycleRecordPermission) {
	fileRecords := make([]*mapper.RecycleRecordFile, len(recycleList))
	permissions := make([]*mapper.RecycleRecordPermission, 0)

	for i, recycleItem := range recycleList {
		// 创建文件记录
		fileRecord := &mapper.RecycleRecordFile{
			ID:                     d.idGenerator.GenerateIDString(),
			RecordID:               recordID,
			DistributeRecordFileID: recycleItem.InventoryID,
		}
		fileRecords[i] = fileRecord

		// 创建权限记录
		for _, permission := range recycleItem.Permissions {
			for _, userID := range permission.ReceivedBy {
				permissionRecord := &mapper.RecycleRecordPermission{
					ID:             d.idGenerator.GenerateIDString(),
					FileRecordID:   fileRecord.ID,
					UserID:         userID,
					FileForm:       int32(permission.FileForm),
					FilePermission: int32(permission.FilePermission),
				}
				permissions = append(permissions, permissionRecord)
			}
		}
	}

	return fileRecords, permissions
}

// executeRecycleSaveTransaction 在事务中执行回收信息保存和权限状态更新
// 参数:
//   - ctx: 上下文
//   - recycleApprovalRequest: 回收审批信息
//   - record: 回收主记录
//   - fileRecords: 回收文件记录列表
//   - permissions: 回收权限记录列表
//
// 返回:
//   - error: 执行失败时返回错误
func (d *DocumentLibraryReferenceImpl) executeRecycleSaveTransaction(ctx context.Context, recycleApprovalRequest *entity.RecycleApprovalInfo, record *mapper.RecycleRecord, fileRecords []*mapper.RecycleRecordFile, permissions []*mapper.RecycleRecordPermission) error {
	// 开始事务
	tx, err := mapper.NewTransaction(d.db.GetDB()).Begin(ctx)
	if err != nil {
		return fmt.Errorf("开始事务失败: %v", err)
	}

	// 使用 defer 和 AutoCommit 来自动处理事务提交或回滚
	defer tx.AutoCommit(&err)

	// 执行数据库操作
	if err = d.saveRecycleRecordsInTransaction(ctx, tx, record, fileRecords, permissions); err != nil {
		return err
	}

	// 更新发放权限表中的处置状态
	if err = d.updateDistributePermissionStatusInTransaction(ctx, tx, recycleApprovalRequest.RecycleList); err != nil {
		return err
	}

	logc.Infof(ctx, "成功保存回收信息并更新发放权限表处置状态，发放记录ID: %s", recycleApprovalRequest.DistributeID)
	return nil
}

// saveRecycleRecordsInTransaction 在事务中保存回收相关记录
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - record: 回收主记录
//   - fileRecords: 回收文件记录列表
//   - permissions: 回收权限记录列表
//
// 返回:
//   - error: 保存失败时返回错误
func (d *DocumentLibraryReferenceImpl) saveRecycleRecordsInTransaction(ctx context.Context, tx mapper.Transaction, record *mapper.RecycleRecord, fileRecords []*mapper.RecycleRecordFile, permissions []*mapper.RecycleRecordPermission) error {
	recordClient := mapper.NewRecycleRecordClient(d.db)
	fileClient := mapper.NewRecycleRecordFileClient(d.db)
	permissionClient := mapper.NewRecycleRecordPermissionClient(d.db)

	// 插入 RecycleRecord
	if err := recordClient.CreateWithTx(ctx, tx, record); err != nil {
		return fmt.Errorf("创建回收记录失败: %v", err)
	}

	// 批量插入 RecycleRecordPermission
	if err := permissionClient.BatchCreateWithTx(ctx, tx, permissions); err != nil {
		return fmt.Errorf("批量创建回收权限记录失败: %v", err)
	}

	// 批量插入 RecycleRecordFile
	if err := fileClient.BatchCreateWithTx(ctx, tx, fileRecords); err != nil {
		return fmt.Errorf("批量创建回收文件记录失败: %v", err)
	}

	return nil
}

// updateDistributePermissionStatusInTransaction 在事务中更新发放权限表的处置状态
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - recycleList: 回收清单列表
//
// 返回:
//   - error: 更新失败时返回错误
func (d *DocumentLibraryReferenceImpl) updateDistributePermissionStatusInTransaction(ctx context.Context, tx mapper.Transaction, recycleList []entity.RecycleList) error {
	distributePermissionClient := mapper.NewDistributeRecordPermissionClient(d.db)

	for _, recycleItem := range recycleList {
		for _, permission := range recycleItem.Permissions {
			if len(permission.ReceivedBy) == 0 {
				continue
			}

			// 更新发放记录权限表中的处置状态为已回收（状态值：3）
			err := distributePermissionClient.UpdateDisposeStatusByConditionsWithTx(
				ctx,
				tx,
				recycleItem.InventoryID, // DistributeRecordFileID
				int32(permission.FileForm),
				int32(permission.FilePermission),
				permission.ReceivedBy,
				3, // 已回收状态
			)
			if err != nil {
				logc.Errorf(ctx, "更新发放记录权限表处置状态失败: %v", err)
				return fmt.Errorf("更新发放记录权限表处置状态失败: %v", err)
			}
		}
	}

	return nil
}

// buildDisposalRecords 构建处置记录相关的数据结构
// 参数:
//   - ctx: 上下文
//   - disposalApprovalRequest: 处置审批信息
//
// 返回:
//   - *mapper.DisposalRecord: 处置主记录
//   - []*mapper.DisposalRecordFile: 处置文件记录列表
//   - []*mapper.DisposalRecordPermission: 处置权限记录列表
//   - error: 构建失败时返回错误
func (d *DocumentLibraryReferenceImpl) buildDisposalRecords(_ context.Context, disposalApprovalRequest *entity.DisposalApprovalInfo) (*mapper.DisposalRecord, []*mapper.DisposalRecordFile, []*mapper.DisposalRecordPermission, error) {
	// 序列化审批信息为 datatypes.JSON
	approvalInfoJSON, err := json.Marshal(disposalApprovalRequest.ApprovalInfo)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("序列化审批信息失败: %v", err)
	}

	// 创建主记录
	record := &mapper.DisposalRecord{
		ID:                 d.idGenerator.GenerateIDString(),
		DistributeRecordID: disposalApprovalRequest.DistributeID,
		DisposalBy:         disposalApprovalRequest.DisposalBy,
		DisposalDate:       disposalApprovalRequest.DisposalDate,
		Reason:             disposalApprovalRequest.DisposalReason,
		WorkflowID:         disposalApprovalRequest.WorkflowID,
		ApprovalInfo:       approvalInfoJSON,
	}

	// 构建文件记录和权限记录
	fileRecords, permissions := d.buildDisposalFileAndPermissionRecords(record.ID, disposalApprovalRequest.DisposalList)

	return record, fileRecords, permissions, nil
}

// buildDisposalFileAndPermissionRecords 构建处置文件记录和权限记录
// 参数:
//   - recordID: 处置记录ID
//   - disposalList: 处置清单列表
//
// 返回:
//   - []*mapper.DisposalRecordFile: 处置文件记录列表
//   - []*mapper.DisposalRecordPermission: 处置权限记录列表
func (d *DocumentLibraryReferenceImpl) buildDisposalFileAndPermissionRecords(recordID string, disposalList []entity.DisposalList) ([]*mapper.DisposalRecordFile, []*mapper.DisposalRecordPermission) {
	fileRecords := make([]*mapper.DisposalRecordFile, len(disposalList))
	permissions := make([]*mapper.DisposalRecordPermission, 0)

	for i, disposalItem := range disposalList {
		// 创建文件记录
		fileRecord := &mapper.DisposalRecordFile{
			ID:                   d.idGenerator.GenerateIDString(),
			RecordID:             recordID,
			DisposalRecordFileID: disposalItem.InventoryID,
		}
		fileRecords[i] = fileRecord

		// 创建权限记录
		for _, permission := range disposalItem.Permissions {
			for _, userID := range permission.ReceivedBy {
				permissionRecord := &mapper.DisposalRecordPermission{
					ID:             d.idGenerator.GenerateIDString(),
					FileRecordID:   fileRecord.ID,
					UserID:         userID,
					FileForm:       int32(permission.FileForm),
					FilePermission: int32(permission.FilePermission),
				}
				permissions = append(permissions, permissionRecord)
			}
		}
	}

	return fileRecords, permissions
}

// executeDisposalSaveTransaction 在事务中执行处置信息保存和权限状态更新
// 参数:
//   - ctx: 上下文
//   - disposalApprovalRequest: 处置审批信息
//   - record: 处置主记录
//   - fileRecords: 处置文件记录列表
//   - permissions: 处置权限记录列表
//
// 返回:
//   - error: 执行失败时返回错误
func (d *DocumentLibraryReferenceImpl) executeDisposalSaveTransaction(ctx context.Context, disposalApprovalRequest *entity.DisposalApprovalInfo, record *mapper.DisposalRecord, fileRecords []*mapper.DisposalRecordFile, permissions []*mapper.DisposalRecordPermission) error {
	// 开始事务
	tx, err := mapper.NewTransaction(d.db.GetDB()).Begin(ctx)
	if err != nil {
		return fmt.Errorf("开始事务失败: %v", err)
	}

	// 使用 defer 和 AutoCommit 来自动处理事务提交或回滚
	defer tx.AutoCommit(&err)

	// 执行数据库操作
	if err = d.saveDisposalRecordsInTransaction(ctx, tx, record, fileRecords, permissions); err != nil {
		return err
	}

	// 更新发放权限表中的处置状态为已处置（状态值：5）
	if err = d.updateDistributePermissionStatusForDisposal(ctx, tx, disposalApprovalRequest.DisposalList); err != nil {
		return err
	}

	logc.Infof(ctx, "成功保存处置信息并更新发放权限表处置状态，发放记录ID: %s", disposalApprovalRequest.DistributeID)
	return nil
}

// saveDisposalRecordsInTransaction 在事务中保存处置相关记录
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - record: 处置主记录
//   - fileRecords: 处置文件记录列表
//   - permissions: 处置权限记录列表
//
// 返回:
//   - error: 保存失败时返回错误
func (d *DocumentLibraryReferenceImpl) saveDisposalRecordsInTransaction(ctx context.Context, tx mapper.Transaction, record *mapper.DisposalRecord, fileRecords []*mapper.DisposalRecordFile, permissions []*mapper.DisposalRecordPermission) error {
	recordClient := mapper.NewDisposalRecordClient(d.db)
	fileClient := mapper.NewDisposalRecordFileClient(d.db)
	permissionClient := mapper.NewDisposalRecordPermissionClient(d.db)

	// 插入 DisposalRecord
	if err := recordClient.CreateWithTx(ctx, tx, record); err != nil {
		return fmt.Errorf("创建处置记录失败: %v", err)
	}

	// 批量插入 DisposalRecordFile
	if err := fileClient.BatchCreateWithTx(ctx, tx, fileRecords); err != nil {
		return fmt.Errorf("批量创建处置文件记录失败: %v", err)
	}

	// 批量插入 DisposalRecordPermission
	if err := permissionClient.BatchCreateWithTx(ctx, tx, permissions); err != nil {
		return fmt.Errorf("批量创建处置权限记录失败: %v", err)
	}

	return nil
}

// updateDistributePermissionStatusForDisposal 在事务中更新发放权限表的处置状态为已处置
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - disposalList: 处置清单列表
//
// 返回:
//   - error: 更新失败时返回错误
func (d *DocumentLibraryReferenceImpl) updateDistributePermissionStatusForDisposal(ctx context.Context, tx mapper.Transaction, disposalList []entity.DisposalList) error {
	distributePermissionClient := mapper.NewDistributeRecordPermissionClient(d.db)

	for _, disposalItem := range disposalList {
		for _, permission := range disposalItem.Permissions {
			if len(permission.ReceivedBy) == 0 {
				continue
			}

			// 更新发放记录权限表中的处置状态为已处置（状态值：5）
			err := distributePermissionClient.UpdateDisposeStatusByConditionsWithTx(
				ctx,
				tx,
				disposalItem.InventoryID, // DistributeRecordFileID
				int32(permission.FileForm),
				int32(permission.FilePermission),
				permission.ReceivedBy,
				5, // 已处置状态
			)
			if err != nil {
				logc.Errorf(ctx, "更新发放记录权限表处置状态失败: %v", err)
				return fmt.Errorf("更新发放记录权限表处置状态失败: %v", err)
			}
		}
	}

	return nil
}
