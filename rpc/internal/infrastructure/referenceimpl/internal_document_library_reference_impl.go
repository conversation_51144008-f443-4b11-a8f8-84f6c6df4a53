package referenceimpl

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/domain/entity"
	"docvault/rpc/internal/domain/value"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/utils"

	"github.com/zeromicro/go-zero/core/logc"
)

type InternalDocumentLibraryReferenceImpl struct {
	db *mapper.DocvaultDB
}

func NewInternalDocumentLibraryReferenceImpl(docvaultDB *mapper.DocvaultDB) aggregate.InternalDocumentLibraryReference {
	return &InternalDocumentLibraryReferenceImpl{
		db: docvaultDB,
	}
}

func (i *InternalDocumentLibraryReferenceImpl) Create(ctx context.Context, tx mapper.Transaction, internalDocument entity.InternalDocument) error {
	// 1. 转换并创建主表记录
	mainRec := mapper.InternalDocumentLibrary{}
	if err := utils.StructCopy(ctx, &mainRec, internalDocument); err != nil {
		return err
	}
	mainRec.SetApprovalInfo(i.buildMapperApprovalInfo(ctx, internalDocument.ApprovalInfo))
	if err := mapper.NewInternalDocumentLibraryClient(i.db).CreateWithTX(ctx, tx, mainRec); err != nil {
		return err
	}

	return nil

}

func (i *InternalDocumentLibraryReferenceImpl) Update(ctx context.Context, tx mapper.Transaction, internalDocument entity.InternalDocument) error {
	// 1. 转换并更新主表记录
	mainRec := &mapper.InternalDocumentLibrary{}
	if err := utils.StructCopy(ctx, &mainRec, internalDocument); err != nil {
		return err
	}

	// mainRec.SetApprovalInfo(i.buildMapperApprovalInfo(ctx, internalDocument.ApprovalInfo))

	if err := tx.GetTX().Model(mainRec).Where("id = ?", mainRec.ID).Save(mainRec).Error; err != nil {
		return err
	}

	return nil
}

func (i *InternalDocumentLibraryReferenceImpl) Get(ctx context.Context, id string) (entity.InternalDocument, error) {
	mainRec, err := mapper.NewInternalDocumentLibraryClient(i.db).GetByID(ctx, id)
	if err != nil {
		logc.Error(ctx, "InternalDocumentLibraryReferenceImpl.Get", "GetByID", err)
		return entity.InternalDocument{}, err
	}

	approvalInfo := mainRec.GetApprovalInfo()
	var approvalInfoValue value.ApprovalInfo
	if err := utils.StructDeepCopy(ctx, &approvalInfoValue, approvalInfo); err != nil {
		return entity.InternalDocument{}, err
	}
	var internalDocument entity.InternalDocument
	if err := utils.StructCopy(ctx, &internalDocument, mainRec); err != nil {
		return entity.InternalDocument{}, err
	}
	internalDocument.ApprovalInfo = approvalInfoValue

	return internalDocument, nil
}

func (i *InternalDocumentLibraryReferenceImpl) CreateHistory(ctx context.Context, tx mapper.Transaction, historyVersion value.InternalDocumentHistory) error {
	historyRec := mapper.InternalDocumentLibraryHistory{}
	if err := utils.StructCopy(ctx, &historyRec, historyVersion); err != nil {
		return err
	}
	return mapper.NewInternalDocumentLibraryHistoryClient(i.db).CreateWithTX(ctx, tx, historyRec)
}

func (i *InternalDocumentLibraryReferenceImpl) GetCountByNames(ctx context.Context, names []string, organizationID string) (int64, error) {
	count, err := mapper.NewInternalDocumentLibraryClient(i.db).GetCountByNames(ctx, names, organizationID)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (i *InternalDocumentLibraryReferenceImpl) GetNoMaxAndLockByNoPrefix(ctx context.Context, tx mapper.Transaction, noPrefix string, organizationID string) (int, error) {
	return mapper.NewInternalDocumentLibraryClient(i.db).GetPrefixMaxAndLock(ctx, tx, noPrefix, organizationID)
}

func (i *InternalDocumentLibraryReferenceImpl) buildMapperApprovalInfo(ctx context.Context, approvalInfo value.ApprovalInfo) mapper.ApprovalInfo {
	var mapperApprovalInfo mapper.ApprovalInfo
	if err := utils.StructDeepCopy(ctx, &mapperApprovalInfo, approvalInfo); err != nil {
		logc.Error(ctx, "InternalDocumentLibraryReferenceImpl.buildMapperApprovalInfo", "StructDeepCopy", err)
		return mapper.ApprovalInfo{}
	}
	return mapperApprovalInfo
}
