package serviceimpl

import (
	"context"
	"docvault/rpc/internal/domain/aggregate"
	"docvault/rpc/internal/domain/root"
	"docvault/rpc/internal/infrastructure/adapter/mapper"
	"docvault/utils"

	"github.com/zeromicro/go-zero/core/logc"
)

// BorrowRecordServiceImpl 借阅记录服务实现
type BorrowRecordServiceImpl struct {
	db                    *mapper.DocvaultDB
	basicAbilityReference aggregate.BasicAbilityReference
	borrowRecordClient    *mapper.BorrowRecordClient
	relationClient        *mapper.BorrowDocumentRelationClient
}

// NewBorrowRecordServiceImpl 创建借阅记录服务实现实例
// 功能: 创建并初始化借阅记录服务实现
// 参数:
//
//	docvaultDB: 数据库连接实例
//	basicAbilityReference: 基础能力引用
//
// 返回值:
//
//	aggregate.BorrowRecordService: 借阅记录服务接口实现
//
// 异常: 无
func NewBorrowRecordServiceImpl(docvaultDB *mapper.DocvaultDB, basicAbilityReference aggregate.BasicAbilityReference) aggregate.BorrowRecordService {
	return &BorrowRecordServiceImpl{
		db:                    docvaultDB,
		basicAbilityReference: basicAbilityReference,
		borrowRecordClient:    mapper.NewBorrowRecordClient(docvaultDB),
		relationClient:        mapper.NewBorrowDocumentRelationClient(docvaultDB),
	}
}

// CreateBorrowRecord 创建借阅记录
// 功能: 创建新的借阅记录，同时创建借阅文档关系记录
// 参数:
//
//	br: 借阅记录根实体
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常、业务逻辑验证异常
func (b *BorrowRecordServiceImpl) CreateBorrowRecord(br root.BorrowRecord) error {
	// 实现步骤:
	// 1. 开启事务
	// 2. 转换并创建借阅记录
	// 3. 创建借阅文档关系记录
	// 4. 提交事务

	ctx := context.Background()

	// 1. 开启事务
	tx := mapper.NewTransaction(b.db.GetDB())
	tx, err := tx.Begin(ctx)
	if err != nil {
		logc.Error(ctx, "BorrowRecordServiceImpl.CreateBorrowRecord", "Begin transaction failed", err)
		return err
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	// 2. 转换并创建借阅记录
	if err := b.createBorrowRecordInTx(ctx, tx, br); err != nil {
		tx.Rollback()
		return err
	}

	// 3. 创建借阅文档关系记录
	if err := b.createDocumentRelationsInTx(ctx, tx, br); err != nil {
		tx.Rollback()
		return err
	}

	// 4. 提交事务
	if err := tx.Commit(); err != nil {
		logc.Error(ctx, "BorrowRecordServiceImpl.CreateBorrowRecord", "Commit transaction failed", err)
		return err
	}

	return nil
}

// createBorrowRecordInTx 在事务中创建借阅记录
// 功能: 转换借阅记录实体并在事务中创建数据库记录
// 参数:
//
//	ctx: 上下文对象
//	tx: 事务实例
//	br: 借阅记录根实体
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (b *BorrowRecordServiceImpl) createBorrowRecordInTx(ctx context.Context, tx mapper.Transaction, br root.BorrowRecord) error {
	// 实现步骤:
	// 1. 转换实体为数据库模型
	// 2. 使用BorrowRecordClient在事务中创建记录

	// 1. 转换实体为数据库模型
	var mapperRecord mapper.BorrowRecord
	if err := utils.StructCopy(ctx, &mapperRecord, &br); err != nil {
		logc.Error(ctx, "BorrowRecordServiceImpl.createBorrowRecordInTx", "Struct copy failed", err)
		return err
	}
	mapperRecord.ApprovalStatus = int32(br.ApprovalStatus)

	// 2. 使用BorrowRecordClient在事务中创建记录
	if err := b.borrowRecordClient.CreateWithTX(ctx, tx, &mapperRecord); err != nil {
		logc.Error(ctx, "BorrowRecordServiceImpl.createBorrowRecordInTx", "Create borrow record failed", err)
		return err
	}

	return nil
}

// createDocumentRelationsInTx 在事务中创建借阅文档关系记录
// 功能: 批量创建借阅文档关系记录
// 参数:
//
//	ctx: 上下文对象
//	tx: 事务实例
//	br: 借阅记录根实体
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (b *BorrowRecordServiceImpl) createDocumentRelationsInTx(ctx context.Context, tx mapper.Transaction, br root.BorrowRecord) error {
	// 实现步骤:
	// 1. 遍历文档列表构建关系记录
	// 2. 使用BorrowDocumentRelationClient批量创建关系记录

	// 1. 遍历文档列表构建关系记录
	var relations []mapper.BorrowDocumentRelation
	for _, doc := range br.Documents {
		var relation mapper.BorrowDocumentRelation
		if err := utils.StructCopy(ctx, &relation, &doc); err != nil {
			logc.Error(ctx, "BorrowRecordServiceImpl.createDocumentRelationsInTx", "Struct copy failed", err)
			return err
		}
		relation.ID = b.basicAbilityReference.GenerateID()
		relation.BorrowRecordID = br.ID
		relation.BorrowStatus = int32(doc.BorrowStatus)
		if !doc.RecoverTime.IsZero() {
			relation.RecoverTime = &doc.RecoverTime
		}
		relations = append(relations, relation)
	}

	// 2. 使用BorrowDocumentRelationClient批量创建关系记录
	if err := b.relationClient.CreateBatchWithTX(ctx, tx, relations); err != nil {
		logc.Error(ctx, "BorrowRecordServiceImpl.createDocumentRelationsInTx", "Create document relations failed", err)
		return err
	}

	return nil
}

// ModifyBorrowRecord 修改借阅记录
// 功能: 修改现有的借阅记录，包含状态变更的业务逻辑验证
// 参数:
//
//	br: 借阅记录根实体
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常、业务逻辑验证异常
func (b *BorrowRecordServiceImpl) ModifyBorrowRecord(br root.BorrowRecord) error {
	// 实现步骤:
	// 1. 开启事务
	// 2. 更新借阅记录
	// 3. 更新借阅文档关系状态
	// 4. 提交事务

	ctx := context.Background()

	// 1. 开启事务
	tx := mapper.NewTransaction(b.db.GetDB())
	tx, err := tx.Begin(ctx)
	if err != nil {
		logc.Error(ctx, "BorrowRecordServiceImpl.ModifyBorrowRecord", "Begin transaction failed", err)
		return err
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	// 2. 更新借阅记录
	var mapperRecord mapper.BorrowRecord
	utils.StructCopy(ctx, &mapperRecord, &br)
	mapperRecord.ApprovalStatus = int32(br.ApprovalStatus)

	if err := b.borrowRecordClient.UpdateWithTX(ctx, tx, &mapperRecord); err != nil {
		logc.Error(ctx, "BorrowRecordServiceImpl.ModifyBorrowRecord", "Update borrow record failed", err)
		tx.Rollback()
		return err
	}

	// 3. 更新借阅文档关系状态
	for _, doc := range br.Documents {
		// 先查询现有的关系记录
		existingRelations, err := b.relationClient.GetByBorrowRecordID(ctx, br.ID)
		if err != nil {
			logc.Error(ctx, "BorrowRecordServiceImpl.ModifyBorrowRecord", "Get existing relations failed", err)
			tx.Rollback()
			return err
		}

		// 找到对应的关系记录并更新
		for _, existingRelation := range existingRelations {
			if existingRelation.DocumentID == doc.DocumentID {
				var updatedRelation mapper.BorrowDocumentRelation
				utils.StructCopy(ctx, &updatedRelation, &doc)
				updatedRelation.ID = existingRelation.ID
				updatedRelation.BorrowRecordID = br.ID
				updatedRelation.BorrowStatus = int32(doc.BorrowStatus)
				if !doc.RecoverTime.IsZero() {
					updatedRelation.RecoverTime = &doc.RecoverTime
				}
				updatedRelation.CreatedAt = existingRelation.CreatedAt
				updatedRelation.CreatedBy = existingRelation.CreatedBy
				updatedRelation.UpdatedBy = existingRelation.UpdatedBy

				if err := b.relationClient.UpdateWithTX(ctx, tx, &updatedRelation); err != nil {
					logc.Error(ctx, "BorrowRecordServiceImpl.ModifyBorrowRecord", "Update document relation failed", err)
					tx.Rollback()
					return err
				}
				break
			}
		}
	}

	// 4. 提交事务
	if err := tx.Commit(); err != nil {
		logc.Error(ctx, "BorrowRecordServiceImpl.ModifyBorrowRecord", "Commit transaction failed", err)
		return err
	}

	return nil
}
